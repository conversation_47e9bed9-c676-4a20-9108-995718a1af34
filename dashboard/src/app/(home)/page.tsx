// Homepage using original components with SpacedFeatures
import { CreditsSection } from "@/components/CreditsSection";
import { FaqSection } from "@/components/FaqSection";
import { SpacedFeatures } from "@/components/SpacedFeatures";
import { HeroSection } from "@/components/Hero";
import { CTASection } from "@/components/CTASection";
import { OpenSourceSection } from "@/components/OpenSourceSection";
import { HomePageSchemas } from "@/components/HomePageSchemas";
import type { Metadata } from "next";

export const metadata: Metadata = {
  title: "InterChat - Connect Discord Servers with Cross-Server Chat",
  description: "InterChat bridges your Discord servers, creating cross-server communication while maintaining complete moderation control. Join thousands of communities worldwide.",
  keywords: ["Discord bot", "cross-server chat", "Discord communities", "server bridge", "Discord moderation"],
  openGraph: {
    title: "InterChat - Connect Discord Servers",
    description: "Bridge Discord servers with cross-server chat, community hubs, and advanced moderation tools.",
    url: "https://interchat.tech",
    siteName: "InterChat",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "InterChat - Connect Discord Servers",
    description: "Bridge Discord servers with cross-server chat, community hubs, and advanced moderation tools.",
  },
};

export default function HomePage() {
  return (
    <>
      {/* Schema.org markup for SEO */}
      <HomePageSchemas />

      <main
        className="flex flex-1 flex-col justify-center"
        itemScope
        itemType="https://schema.org/WebPage"
      >
        {/* Original homepage components with spacious features */}
        <HeroSection />
        <SpacedFeatures />
        <FaqSection />
        <OpenSourceSection />
        <CreditsSection />
        <CTASection />
      </main>
    </>
  );
}
