import { type NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/prisma";
import { z } from "zod/v4";
import { getUserHubPermission } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";
import { withAuthRateLimit, withStrictRateLimit } from "@/lib/rate-limit-middleware";
import { ENDPOINT_RATE_LIMITS } from "@/lib/rate-limit-config";

// Schema for updating an appeal
const updateAppealSchema = z.object({
	status: z.enum(["PENDING", "ACCEPTED", "REJECTED"]),
});

// GET a specific appeal
async function handleGET(
	request: NextRequest,
	props: { params: Promise<{ appealId: string }> },
) {
	try {
		const session = await auth();
		if (!session?.user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { appealId } = await props.params;

		// Get the appeal
		const appeal = await db.appeal.findUnique({
			where: { id: appealId },
			include: {
				user: {
					select: {
						id: true,
						name: true,
						image: true,
					},
				},
				infraction: {
					include: {
						hub: {
							select: {
								id: true,
								name: true,
								iconUrl: true,
								ownerId: true,
								moderators: {
									where: { userId: session.user.id },
									select: { userId: true, role: true },
								},
							},
						},
						user: {
							select: {
								id: true,
								name: true,
								image: true,
							},
						},
						moderator: {
							select: {
								id: true,
								name: true,
								image: true,
							},
						},
					},
				},
			},
		});

		if (!appeal) {
			return NextResponse.json({ error: "Appeal not found" }, { status: 404 });
		}

		// Check if the user has permission to view this appeal
		const isOwner = appeal.userId === session.user.id;
		const hubId = appeal.infraction.hubId;
		const permissionLevel = await getUserHubPermission(session.user.id, hubId);
		const canModerate = permissionLevel >= PermissionLevel.MODERATOR;

		if (!isOwner && !canModerate) {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		return NextResponse.json({ appeal });
	} catch (error) {
		console.error("Error fetching appeal:", error);
		return NextResponse.json(
			{ error: "Failed to fetch appeal" },
			{ status: 500 },
		);
	}
}

// PATCH (update) an appeal
async function handlePATCH(
	request: NextRequest,
	props: { params: Promise<{ appealId: string }> },
) {
	try {
		const session = await auth();
		if (!session?.user) {
			return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
		}

		const { appealId } = await props.params;

		// Get the appeal
		const appeal = await db.appeal.findUnique({
			where: { id: appealId },
			include: {
				infraction: true,
			},
		});

		if (!appeal) {
			return NextResponse.json({ error: "Appeal not found" }, { status: 404 });
		}

		// Check if the user has permission to update this appeal
		const hubId = appeal.infraction.hubId;
		const permissionLevel = await getUserHubPermission(session.user.id, hubId);

		if (permissionLevel < PermissionLevel.MODERATOR) {
			return NextResponse.json({ error: "Forbidden" }, { status: 403 });
		}

		// Parse and validate the request body
		const body = await request.json();
		const validation = updateAppealSchema.safeParse(body);

		if (!validation.success) {
			return NextResponse.json(
				{ error: "Invalid input", details: validation.error.errors },
				{ status: 400 },
			);
		}

		const { status } = validation.data;

		// If the appeal is already in the requested status, return early
		if (appeal.status === status) {
			return NextResponse.json({ appeal });
		}

		// Start a transaction to update both the appeal and infraction if needed
		const result = await db.$transaction(async (tx) => {
			// Update the appeal
			const updatedAppeal = await tx.appeal.update({
				where: { id: appealId },
				data: { status },
				include: {
					user: {
						select: {
							id: true,
							name: true,
							image: true,
						},
					},
					infraction: true,
				},
			});

			// If the appeal is accepted, update the infraction status
			if (status === "ACCEPTED") {
				await tx.infraction.update({
					where: { id: appeal.infractionId },
					data: { status: "APPEALED" },
				});
			}

			return updatedAppeal;
		});

		return NextResponse.json({ appeal: result });
	} catch (error) {
		console.error("Error updating appeal:", error);
		return NextResponse.json(
			{ error: "Failed to update appeal" },
			{ status: 500 },
		);
	}
}

// Apply rate limiting to the handlers
export const GET = withAuthRateLimit(handleGET, {
	tier: ENDPOINT_RATE_LIMITS.APPEALS.LIST,
});

export const PATCH = withStrictRateLimit(handlePATCH, {
	tier: ENDPOINT_RATE_LIMITS.APPEALS.RESPOND,
	customMessage: "Appeal response rate limit exceeded. Please wait before responding to another appeal.",
});
