import { auth } from "@/auth";
import { PermissionLevel } from "@/lib/constants";
import { getUserHubPermission } from "@/lib/permissions";
import { db } from "@/lib/prisma";
import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod/v4";

// Helper function to generate Discord invite using bot token
async function generateDiscordInvite(channelId: string): Promise<string | null> {
  try {
    const botToken = process.env.DISCORD_BOT_TOKEN;
    if (!botToken) {
      console.error("Discord bot token not configured");
      return null;
    }

    // Create invite using Discord REST API
    const response = await fetch(`https://discord.com/api/v10/channels/${channelId}/invites`, {
      method: "POST",
      headers: {
        "Authorization": `Bot ${botToken}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        max_age: 0, // Never expires
        max_uses: 0, // Unlimited uses
        temporary: false, // Permanent membership
        unique: false, // Reuse existing invites when possible
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));

      if (response.status === 403) {
        console.error(`Bot missing 'Create Instant Invite' permission in channel ${channelId}`);
        throw new Error("Bot needs 'Create Instant Invite' permission in this channel");
      } else if (response.status === 404) {
        console.error(`Channel ${channelId} not found or bot lacks access`);
        throw new Error("Channel not found or bot lacks access");
      } else if (response.status === 429) {
        console.error("Discord API rate limit hit while creating invite");
        throw new Error("Discord API rate limit exceeded. Please try again later.");
      } else {
        console.error(`Discord API error creating invite: ${response.status}`, errorData);
        throw new Error(`Failed to create invite: ${errorData.message || 'Unknown error'}`);
      }
    }

    const inviteData = await response.json();

    // Validate response structure
    if (!inviteData.code) {
      console.error("Invalid invite response from Discord API:", inviteData);
      throw new Error("Invalid response from Discord API");
    }

    return `https://discord.gg/${inviteData.code}`;
  } catch (error) {
    console.error("Error generating Discord invite:", error);

    // Re-throw known errors with user-friendly messages
    if (error instanceof Error) {
      throw error;
    }

    return null;
  }
}

// Schema for updating a connection
const updateConnectionSchema = z.object({
  connected: z.boolean().optional(),
  invite: z.url().optional().nullable(),
  channelId: z.string().optional(),
});

export async function GET(
  _request: NextRequest,
  props: { params: Promise<{ connectionId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { connectionId } = await props.params;

    const connection = await db.connection.findUnique({
      where: { id: connectionId },
      select: {
        id: true,
        channelId: true,
        parentId: true,
        serverId: true,
        hubId: true,
        connected: true,
        invite: true,
        createdAt: true,
        lastActive: true,
        hub: {
          select: {
            id: true,
            name: true,
            description: true,
            ownerId: true,
            iconUrl: true,
            bannerUrl: true,
            welcomeMessage: true,
            private: true,
            locked: true,
            appealCooldownHours: true,
            lastActive: true,
            settings: true,
            createdAt: true,
            updatedAt: true,
            nsfw: true,
            verified: true,
            partnered: true,
            language: true,
            region: true,
            rules: true,
          },
        },
        server: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
            createdAt: true,
            premiumStatus: true,
            updatedAt: true,
            inviteCode: true,
            messageCount: true,
            lastMessageAt: true,
          },
        },
      },
    });

    if (!connection) {
      return NextResponse.json({ error: "Connection not found" }, { status: 404 });
    }

    // Check if user has permission to view this connection
    // User needs either hub permissions OR Discord server "Manage Channels" permission
    const hubPermissionLevel = await getUserHubPermission(session.user.id, connection.hubId);

    // If user has hub permissions, they can view
    if (hubPermissionLevel > PermissionLevel.NONE) {
      return NextResponse.json({ connection });
    }

    // If no hub permissions, check if they have "Manage Channels" permission on the Discord server
    try {
      const { getServers } = await import("@/actions/server-actions");
      const serversResult = await getServers();

      if ("data" in serversResult) {
        const userServers = serversResult.data;
        // getServers() already filters for servers where user has Manage Channels permission (0x10)
        // So if the server is in the list, the user has the required permissions
        const hasServerAccess = userServers.some(server =>
          server.connections.some(conn => conn.id === connectionId)
        );

        if (hasServerAccess) {
          return NextResponse.json({ connection });
        }
      }
    } catch (error) {
      console.error("Error checking server access:", error);
      // If we can't check server permissions due to rate limiting, deny access for security
    }

    return NextResponse.json({ error: "Forbidden" }, { status: 403 });
  } catch (error) {
    console.error("Error fetching connection:", error);
    return NextResponse.json(
      { error: "Failed to fetch connection" },
      { status: 500 },
    );
  }
}

export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ connectionId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { connectionId } = await props.params;

    // Get the connection to check permissions
    const connection = await db.connection.findUnique({
      where: { id: connectionId },
      select: { hubId: true, server: { select: { id: true } } },
    });

    if (!connection) {
      return NextResponse.json({ error: "Connection not found" }, { status: 404 });
    }

    // Check if user has permission to manage this connection
    // User needs Discord server "Manage Channels" permission ONLY
    // Hub moderators/owners no longer have access to edit individual connections
    try {
      const { getServers } = await import("@/actions/server-actions");
      const serversResult = await getServers();

      if ("data" in serversResult) {
        const userServers = serversResult.data;
        // getServers() already filters for servers where user has Manage Channels permission (0x10)
        const hasServerAccess = userServers.some(server =>
          server.connections.some(conn => conn.id === connectionId)
        );

        if (!hasServerAccess) {
          return NextResponse.json({ error: "Forbidden" }, { status: 403 });
        }
      } else {
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }
    } catch (error) {
      console.error("Error checking server access:", error);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = updateConnectionSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 },
      );
    }

    const { connected, invite, channelId } = validation.data;

    // If channelId is being changed, validate it doesn't already exist
    if (channelId && channelId !== connectionId) {
      const existingConnection = await db.connection.findFirst({
        where: { channelId },
      });

      if (existingConnection) {
        return NextResponse.json(
          { error: "Channel is already connected to a hub" },
          { status: 400 },
        );
      }
    }

    const updatedConnection = await db.connection.update({
      where: { id: connectionId },
      data: {
        ...(connected !== undefined && { connected }),
        ...(invite !== undefined && { invite }),
        ...(channelId !== undefined && { channelId }),
      },
      select: {
        id: true,
        channelId: true,
        parentId: true,
        serverId: true,
        hubId: true,
        connected: true,
        invite: true,
        createdAt: true,
        lastActive: true,
        hub: {
          select: {
            id: true,
            name: true,
            description: true,
            ownerId: true,
            iconUrl: true,
            bannerUrl: true,
            welcomeMessage: true,
            private: true,
            locked: true,
            appealCooldownHours: true,
            lastActive: true,
            settings: true,
            createdAt: true,
            updatedAt: true,
            nsfw: true,
            verified: true,
            partnered: true,
            language: true,
            region: true,
            rules: true,
          },
        },
        server: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
            createdAt: true,
            premiumStatus: true,
            updatedAt: true,
            inviteCode: true,
            messageCount: true,
            lastMessageAt: true,
          },
        },
      },
    });

    return NextResponse.json({ connection: updatedConnection });
  } catch (error) {
    console.error("Error updating connection:", error);
    return NextResponse.json(
      { error: "Failed to update connection" },
      { status: 500 },
    );
  }
}

export async function DELETE(
  _request: NextRequest,
  props: { params: Promise<{ connectionId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { connectionId } = await props.params;

    // Get the connection to check permissions
    const connection = await db.connection.findUnique({
      where: { id: connectionId },
      select: { hubId: true, server: { select: { id: true } } },
    });

    if (!connection) {
      return NextResponse.json({ error: "Connection not found" }, { status: 404 });
    }

    // Check if user has permission to manage this connection
    // User needs Discord server "Manage Channels" permission ONLY
    // Hub moderators/owners no longer have access to delete individual connections
    try {
      const { getServers } = await import("@/actions/server-actions");
      const serversResult = await getServers();

      if ("data" in serversResult) {
        const userServers = serversResult.data;
        // getServers() already filters for servers where user has Manage Channels permission (0x10)
        const hasServerAccess = userServers.some(server =>
          server.connections.some(conn => conn.id === connectionId)
        );

        if (!hasServerAccess) {
          return NextResponse.json({ error: "Forbidden" }, { status: 403 });
        }
      } else {
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }
    } catch (error) {
      console.error("Error checking server access:", error);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    await db.connection.delete({
      where: { id: connectionId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error deleting connection:", error);
    return NextResponse.json(
      { error: "Failed to delete connection" },
      { status: 500 },
    );
  }
}

// POST endpoint for generating invite links
export async function POST(
  request: NextRequest,
  props: { params: Promise<{ connectionId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { connectionId } = await props.params;
    const body = await request.json();
    const { action } = body;

    if (action !== "generate_invite") {
      return NextResponse.json({ error: "Invalid action" }, { status: 400 });
    }

    // Get the connection to check permissions
    const connection = await db.connection.findUnique({
      where: { id: connectionId },
      select: { hubId: true, channelId: true, server: { select: { id: true } } },
    });

    if (!connection) {
      return NextResponse.json({ error: "Connection not found" }, { status: 404 });
    }

    // Check if user has permission to manage this connection
    // User needs Discord server "Manage Channels" permission ONLY
    // Hub moderators/owners no longer have access to generate invites for individual connections
    try {
      const { getServers } = await import("@/actions/server-actions");
      const serversResult = await getServers();

      if ("data" in serversResult) {
        const userServers = serversResult.data;
        // getServers() already filters for servers where user has Manage Channels permission (0x10)
        const hasServerAccess = userServers.some(server =>
          server.connections.some(conn => conn.id === connectionId)
        );

        if (!hasServerAccess) {
          return NextResponse.json({ error: "Forbidden" }, { status: 403 });
        }
      } else {
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }
    } catch (error) {
      console.error("Error checking server access:", error);
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Generate invite link
    let inviteUrl: string;
    try {
      const generatedInvite = await generateDiscordInvite(connection.channelId);
      if (!generatedInvite) {
        return NextResponse.json(
          { error: "Failed to generate invite link" },
          { status: 500 },
        );
      }
      inviteUrl = generatedInvite;
    } catch (error) {
      console.error("Error generating invite:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to generate invite link";

      // Return specific error messages for different scenarios
      if (errorMessage.includes("permission")) {
        return NextResponse.json(
          { error: "Bot needs 'Create Instant Invite' permission in this channel" },
          { status: 403 },
        );
      } else if (errorMessage.includes("not found") || errorMessage.includes("access")) {
        return NextResponse.json(
          { error: "Channel not found or bot lacks access" },
          { status: 404 },
        );
      } else if (errorMessage.includes("rate limit")) {
        return NextResponse.json(
          { error: "Discord API rate limit exceeded. Please try again later." },
          { status: 429 },
        );
      } else {
        return NextResponse.json(
          { error: errorMessage },
          { status: 500 },
        );
      }
    }

    // Update connection with new invite
    const updatedConnection = await db.connection.update({
      where: { id: connectionId },
      data: { invite: inviteUrl },
      select: {
        id: true,
        channelId: true,
        parentId: true,
        serverId: true,
        hubId: true,
        connected: true,
        invite: true,
        createdAt: true,
        lastActive: true,
        hub: {
          select: {
            id: true,
            name: true,
            description: true,
            ownerId: true,
            iconUrl: true,
            bannerUrl: true,
            welcomeMessage: true,
            private: true,
            locked: true,
            appealCooldownHours: true,
            lastActive: true,
            settings: true,
            createdAt: true,
            updatedAt: true,
            nsfw: true,
            verified: true,
            partnered: true,
            language: true,
            region: true,
            rules: true,
          },
        },
        server: {
          select: {
            id: true,
            name: true,
            iconUrl: true,
            createdAt: true,
            premiumStatus: true,
            updatedAt: true,
            inviteCode: true,
            messageCount: true,
            lastMessageAt: true,
          },
        },
      },
    });

    return NextResponse.json({
      connection: updatedConnection,
      inviteUrl
    });
  } catch (error) {
    console.error("Error generating invite:", error);
    return NextResponse.json(
      { error: "Failed to generate invite" },
      { status: 500 },
    );
  }
}
