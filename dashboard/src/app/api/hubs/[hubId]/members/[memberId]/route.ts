import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { db } from "@/lib/prisma";
import { z } from "zod/v4";
import { getUserHubPermission } from "@/lib/permissions";
import { PermissionLevel } from "@/lib/constants";

// Schema for updating a member
const updateMemberSchema = z.object({
  role: z.enum(["MODERATOR", "MANAGER"]),
});

// Get a specific member
export async function GET(
  request: NextRequest,
  props: { params: Promise<{ hubId: string; memberId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId, memberId } = await props.params;

    // Check if the user has permission to view this hub
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel === PermissionLevel.NONE) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the moderator
    const moderator = await db.hubModerator.findUnique({
      where: { id: memberId },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },

    });

    if (!moderator || moderator.hubId !== hubId) {
      return NextResponse.json({ error: "Member not found" }, { status: 404 });
    }

    return NextResponse.json({ moderator });
  } catch (error) {
    console.error("Error fetching hub member:", error);
    return NextResponse.json(
      { error: "Failed to fetch hub member" },
      { status: 500 },
    );
  }
}

// Update a member's role
export async function PATCH(
  request: NextRequest,
  props: { params: Promise<{ hubId: string; memberId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId, memberId } = await props.params;

    // Check if the user has permission to manage members
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the moderator
    const moderator = await db.hubModerator.findUnique({
      where: { id: memberId },

    });

    if (!moderator || moderator.hubId !== hubId) {
      return NextResponse.json({ error: "Member not found" }, { status: 404 });
    }

    // Parse and validate the request body
    const body = await request.json();
    const validation = updateMemberSchema.safeParse(body);

    if (!validation.success) {
      return NextResponse.json(
        { error: "Invalid input", details: validation.error.errors },
        { status: 400 },
      );
    }

    const { role } = validation.data;

    // Update the moderator's role
    const updatedModerator = await db.hubModerator.update({
      where: { id: memberId },
      data: { role },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true,
          },
        },
      },
    });

    return NextResponse.json({ moderator: updatedModerator });
  } catch (error) {
    console.error("Error updating hub member:", error);
    return NextResponse.json(
      { error: "Failed to update hub member" },
      { status: 500 },
    );
  }
}

// Remove a member
export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ hubId: string; memberId: string }> },
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { hubId, memberId } = await props.params;

    // Check if the user has permission to manage members
    const permissionLevel = await getUserHubPermission(session.user.id, hubId);
    if (permissionLevel < PermissionLevel.MANAGER) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get the moderator
    const moderator = await db.hubModerator.findUnique({
      where: { id: memberId },

    });

    if (!moderator || moderator.hubId !== hubId) {
      return NextResponse.json({ error: "Member not found" }, { status: 404 });
    }

    // Delete the moderator
    await db.hubModerator.delete({
      where: { id: memberId },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error removing hub member:", error);
    return NextResponse.json(
      { error: "Failed to remove hub member" },
      { status: 500 },
    );
  }
}
