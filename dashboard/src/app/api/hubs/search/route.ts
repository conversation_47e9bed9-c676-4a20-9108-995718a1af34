import { type NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  const searchTerm = request.nextUrl.searchParams.get('term');

  if (!searchTerm) {
    return NextResponse.json({ hubs: [] });
  }

  try {
    // For the new blur approach, we show all content
    // The frontend will handle blurring NSFW content based on user preferences
    const hubs = await db.hub.findMany({
      where: {
        private: false,
        // Show all content including NSFW - frontend will handle blurring
        OR: [
          { name: { contains: searchTerm, mode: 'insensitive' } },
          { description: { contains: searchTerm, mode: 'insensitive' } },
          { shortDescription: { contains: searchTerm, mode: 'insensitive' } },
          { tags: { some: { name: searchTerm } } },
        ],
      },
      select: {
        id: true,
        name: true,
        description: true,
        shortDescription: true,
        iconUrl: true,
        bannerUrl: true,
        private: true,
        locked: true,
        nsfw: true,
        verified: true,
        partnered: true,
        language: true,
        region: true,
        createdAt: true,
        lastActive: true,

        connections: {
          where: { connected: true },
          orderBy: { lastActive: 'desc' },
          select: {
            id: true,
            serverId: true,
            connected: true,
            createdAt: true,
            lastActive: true,
            server: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
        upvotes: {
          select: {
            id: true,
            userId: true,
            createdAt: true,
          },
        },
        moderators: {
          select: {
            id: true,
            userId: true,
            role: true,

            user: {
              select: {
                id: true,
                name: true,
                image: true,
              },
            },
          },
        },
        tags: {
          select: {
            name: true,
          },
        },
        _count: {
          select: {
            connections: { where: { connected: true } },
            upvotes: true,
            reviews: true,
            messages: true,
          },
        },
      },
      take: 12,
    });

    return NextResponse.json({ hubs });
  } catch (error) {
    console.error('Error searching hubs:', error);
    return NextResponse.json({ error: 'Failed to search hubs' }, { status: 500 });
  }
}
