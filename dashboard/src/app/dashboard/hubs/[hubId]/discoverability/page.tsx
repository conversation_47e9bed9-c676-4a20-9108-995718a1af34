import { auth } from '@/auth';
import { PermissionLevel } from '@/lib/constants';
import { getUserHubPermission } from '@/lib/permissions';
import { db } from '@/lib/prisma';
import type { Metadata } from 'next';
import { notFound, redirect } from 'next/navigation';
import { HubDiscoverabilityForm } from './components/hub-discoverability-form';
import { HubLayout } from '@/components/dashboard/hubs/hub-layout';

interface HubDiscoverabilityPageProps {
  params: Promise<{
    hubId: string;
  }>;
}

export async function generateMetadata({ params }: HubDiscoverabilityPageProps): Promise<Metadata> {
  const { hubId } = await params;
  const hub = await db.hub.findUnique({
    where: { id: hubId },
    select: { name: true },
  });

  return {
    title: hub
      ? `${hub.name} Discoverability | InterChat Dashboard`
      : 'Hub Discoverability | InterChat Dashboard',
    description: 'Manage your InterChat hub discoverability settings, tags, and language',
  };
}

export default async function HubDiscoverabilityPage({ params }: HubDiscoverabilityPageProps) {
  const { hubId } = await params;
  const session = await auth();

  if (!session?.user) {
    redirect(`/login?callbackUrl=/dashboard/hubs/${hubId}/discoverability`);
  }

  const permissionLevel = await getUserHubPermission(session.user.id, hubId);
  const canEdit = permissionLevel >= PermissionLevel.MANAGER;
  const isOwner = permissionLevel === PermissionLevel.OWNER;

  if (permissionLevel === PermissionLevel.NONE) {
    notFound();
  }

  // Fetch hub data with all necessary relations
  const hub = await db.hub.findUnique({
    where: { id: hubId },
    include: {
      tags: {
        select: { name: true },
      },
      _count: {
        select: {
          connections: {
            where: { connected: true },
          },
          upvotes: true,
        },
      },
    },
  });

  if (!hub) {
    notFound();
  }

  // Transform data for client component
  const hubData = {
    id: hub.id,
    name: hub.name,
    description: hub.description,
    private: hub.private,
    welcomeMessage: hub.welcomeMessage,
    rules: hub.rules,
    bannerUrl: hub.bannerUrl,
    iconUrl: hub.iconUrl,
    language: hub.language,
    nsfw: hub.nsfw,
    tags: hub.tags.map((tag) => tag.name),
    connectionCount: hub._count.connections,
    isOwner,
    canEdit,
  };

  return (
    <HubLayout
      hub={hubData}
      currentTab="edit"
      canModerate={permissionLevel >= PermissionLevel.MODERATOR}
      canEdit={canEdit}
    >
      <div className="container mx-auto p-6 space-y-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-white">Hub Discoverability</h1>
            <p className="text-gray-400">
              Manage tags, language, and content settings to help users find your hub
            </p>
          </div>
        </div>

        <HubDiscoverabilityForm hubData={hubData} />
      </div>
    </HubLayout>
  );
}
