"use client";

import { But<PERSON> } from '@/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useToast } from '@/components/ui/use-toast';
import {
    ArrowDown,
    ArrowUp,
    Edit3,
    Loader2,
    MessageSquare,
    Plus,
    RotateCcw,
    Save,
    X
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface HubData {
  id: string;
  name: string;
  description: string;
  private: boolean;
  welcomeMessage: string | null;
  rules: string[];
  bannerUrl: string | null;
  iconUrl: string | null;
  language: string | null;
  nsfw: boolean;
  tags: string[];
  connectionCount: number;
  isOwner: boolean;
  canEdit: boolean;
}

interface HubEditFormProps {
  hubData: HubData;
}

export function HubEditForm({ hubData }: HubEditFormProps) {
  // Original values for comparison
  const originalValues = {
    name: hubData.name,
    description: hubData.description,
    private: hubData.private,
    welcomeMessage: hubData.welcomeMessage || '',
    rules: hubData.rules || [],
  };

  // Form state
  const [name, setName] = useState(hubData.name);
  const [description, setDescription] = useState(hubData.description);
  const [isPrivate, setIsPrivate] = useState(hubData.private);
  const [welcomeMessage, setWelcomeMessage] = useState(hubData.welcomeMessage || '');
  const [rules, setRules] = useState<string[]>(hubData.rules || []);
  const [newRule, setNewRule] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { toast } = useToast();
  const router = useRouter();

  // Check if form has unsaved changes
  const hasUnsavedChanges =
    name !== originalValues.name ||
    description !== originalValues.description ||
    isPrivate !== originalValues.private ||
    welcomeMessage !== originalValues.welcomeMessage ||
    JSON.stringify(rules) !== JSON.stringify(originalValues.rules);

  const supportedWelcomeVariables = [
    '{user}',
    '{hubName}',
    '{serverName}',
    '{memberCount}',
    '{totalConnections}',
  ];

  const formattedWelcomeVariablesList = supportedWelcomeVariables.map((variable) => (
    <code
      key={variable}
      className="bg-indigo-500/20 px-2 py-1 rounded cursor-pointer inline-block hover:bg-indigo-500/30 transition-colors text-indigo-300"
      onClick={() => setWelcomeMessage(welcomeMessage + variable)}
    >
      {variable}
    </code>
  ));

  // Reset form to original values
  const resetForm = () => {
    setName(originalValues.name);
    setDescription(originalValues.description);
    setIsPrivate(originalValues.private);
    setWelcomeMessage(originalValues.welcomeMessage);
    setRules(originalValues.rules);
    setNewRule('');
  };

  // Rule handlers
  const handleAddRule = () => {
    if (newRule.trim() && !rules.includes(newRule.trim())) {
      setRules([...rules, newRule.trim()]);
      setNewRule('');
    }
  };

  const handleRemoveRule = (index: number) => {
    setRules(rules.filter((_, i) => i !== index));
  };

  const handleMoveRuleUp = (index: number) => {
    if (index === 0) return;
    const newRules = [...rules];
    [newRules[index - 1], newRules[index]] = [newRules[index], newRules[index - 1]];
    setRules(newRules);
  };

  const handleMoveRuleDown = (index: number) => {
    if (index === rules.length - 1) return;
    const newRules = [...rules];
    [newRules[index], newRules[index + 1]] = [newRules[index + 1], newRules[index]];
    setRules(newRules);
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch(`/api/hubs/${hubData.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description,
          private: isPrivate,
          welcomeMessage: welcomeMessage || null,
          rules,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update hub');
      }

      toast({
        title: 'Hub Updated',
        description: 'Your hub has been successfully updated.',
      });

      router.refresh();
    } catch (error) {
      console.error('Error updating hub:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to update hub',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Unsaved changes notification component
  const UnsavedChangesNotification = () => {
    if (!hasUnsavedChanges) return null;

    return (
      <div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50">
        <div className="bg-yellow-600 text-white px-4 py-3 rounded-lg shadow-lg flex items-center gap-3">
          <MessageSquare className="h-5 w-5" />
          <span className="font-medium">Careful! You have unsaved changes.</span>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={resetForm}
              className="bg-transparent border-white text-white hover:bg-white hover:text-yellow-600"
            >
              <RotateCcw className="h-4 w-4 mr-1" />
              Reset
            </Button>
            <Button
              size="sm"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="bg-white text-yellow-600 hover:bg-gray-100"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-1" />
                  Save
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-8">
      {/* Basic Information Section */}
      <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm shadow-xl">
        <form onSubmit={handleSubmit}>
          <CardHeader>
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                <Edit3 className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-xl">Basic Information</CardTitle>
                <CardDescription className="text-base">
                  Update your hub&apos;s core settings and content
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="name" className="text-base font-medium">Hub Name</Label>
                <Input
                  id="name"
                  placeholder="Enter hub name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  required
                  minLength={3}
                  maxLength={32}
                  className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50"
                />
                <p className="text-xs text-gray-400">Choose a unique name between 3-32 characters.</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-3">
                  <Switch
                    id="private"
                    checked={isPrivate}
                    onCheckedChange={setIsPrivate}
                  />
                  <Label htmlFor="private" className="text-base font-medium">
                    Private Hub
                  </Label>
                </div>
                <p className="text-xs text-gray-400">
                  Private hubs are only visible to invited members and won&apos;t appear in public listings.
                </p>
              </div>
            </div>

            <div className="space-y-3">
              <Label htmlFor="description" className="text-base font-medium">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe your hub..."
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                required
                minLength={10}
                maxLength={500}
                className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 min-h-[100px] resize-none"
              />
              <div className="flex justify-between text-xs text-gray-400">
                <p>Tell users what your hub is about and what they can expect.</p>
                <span>{description.length}/500</span>
              </div>
            </div>

            <div className="space-y-3">
              <Label htmlFor="welcomeMessage" className="text-base font-medium">
                Welcome Message
              </Label>
              <Textarea
                id="welcomeMessage"
                placeholder="Write a welcome message for new servers..."
                value={welcomeMessage}
                onChange={(e) => setWelcomeMessage(e.target.value)}
                maxLength={1000}
                className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 min-h-[100px] resize-none"
              />
              <div className="flex justify-between items-start">
                <div className="space-y-1">
                  <p className="text-xs text-gray-400">
                    This message will be shown to <b>all servers</b> when a new server joins your hub.
                  </p>
                  <span className="text-xs text-gray-400">{welcomeMessage.length}/1000</span>
                </div>
                <div className="text-xs text-gray-400">
                  <p className="mb-2">You can use these variables:</p>
                  <div className="flex flex-wrap gap-2">
                    {formattedWelcomeVariablesList}
                  </div>
                </div>
              </div>
            </div>

            {/* Rules Section */}
            <div className="space-y-4 pt-6 border-t border-gray-700/50">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Hub Rules</Label>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleAddRule}
                  disabled={!newRule.trim()}
                  className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Rule
                </Button>
              </div>

              <div className="flex gap-3">
                <Textarea
                  placeholder="Add a new rule (e.g., Be respectful to all members)"
                  value={newRule}
                  onChange={(e) => setNewRule(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && e.ctrlKey) {
                      handleAddRule();
                    }
                  }}
                  className="bg-gray-800/50 border-gray-700/50 focus-visible:ring-indigo-500/50 min-h-[80px] resize-none flex-1"
                  maxLength={200}
                />
              </div>

              <div className="space-y-3">
                {rules.length > 0 ? (
                  rules.map((rule, index) => (
                    <div
                      key={index}
                      className="flex items-start gap-3 p-4 rounded-lg bg-gray-800/30 border border-gray-700/50"
                    >
                      <div className="flex flex-col gap-1">
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleMoveRuleUp(index)}
                                disabled={index === 0}
                                className="h-8 w-8 p-0 hover:bg-gray-700/50"
                              >
                                <ArrowUp className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Move up</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleMoveRuleDown(index)}
                                disabled={index === rules.length - 1}
                                className="h-8 w-8 p-0 hover:bg-gray-700/50"
                              >
                                <ArrowDown className="h-4 w-4" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              <p>Move down</p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </div>
                      <div className="flex-1 text-sm text-gray-200">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-indigo-400 font-medium">Rule {index + 1}</span>
                        </div>
                        <p className="leading-relaxed">{rule}</p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveRule(index)}
                        className="h-8 w-8 p-0 hover:bg-red-600/20 hover:text-red-400"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <MessageSquare className="h-12 w-12 mx-auto mb-3 opacity-50" />
                    <p>No rules added yet</p>
                    <p className="text-sm">Add your first rule to help maintain a positive environment.</p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </form>
      </Card>

      {/* Unsaved Changes Notification */}
      <UnsavedChangesNotification />
    </div>
  );
}
