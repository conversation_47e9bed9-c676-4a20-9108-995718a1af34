import { auth } from '@/auth';
import { getServers, type ServerDataWithConnections } from '@/actions/server-actions';
import { AnimatedDashboardSkeleton } from '@/components/dashboard/animated-dashboard-skeleton';
import { StatCard } from '@/components/dashboard/stat-card';
import { AnimatedWelcome } from '@/components/dashboard/animated-welcome';
import { RecentNotifications } from '@/components/dashboard/recent-notifications';
import { UnderlinedTabs } from '@/components/dashboard/underlined-tabs';
import { ServerGrid } from '@/components/dashboard/servers/server-grid';
import { ConnectionsGrid } from '@/components/dashboard/servers/connections-grid';
import { AnimatedHubCard } from '@/components/dashboard/hubs/animated-hub-card';
import { AnimatedEmptyState } from '@/components/dashboard/hubs/animated-empty-state';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { TabsContent } from '@/components/ui/tabs';
import { getUserHubs } from '@/lib/permissions';
import { db } from '@/lib/prisma';
import { PermissionLevel } from '@/lib/constants';
import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { Suspense } from 'react';
import { PlusCircle, MessageSquare, Server } from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Dashboard | InterChat',
  description: 'InterChat Dashboard - Manage your hubs, servers, and users',
};

export default function DashboardPage(props: { searchParams: Promise<{ hubId?: string }> }) {
  return (
    <Suspense fallback={<AnimatedDashboardSkeleton />}>
      <DashboardContent searchParams={props.searchParams} />
    </Suspense>
  );
}

async function DashboardContent({ searchParams }: { searchParams: Promise<{ hubId?: string }> }) {
  const session = await auth();
  const { hubId } = await searchParams;

  if (!session?.user) {
    redirect('/login?callbackUrl=/dashboard');
  }

  // Get user's hubs
  const userHubs = await getUserHubs(session.user.id);

  // If hubId is provided, check if it's a valid hub for connection
  let targetHub = null;
  if (hubId) {
    targetHub = await db.hub.findUnique({
      where: { id: hubId },
      select: {
        id: true,
        name: true,
        description: true,
        iconUrl: true,
        private: true,
      },
    });
  }

  // Get servers using the server action with a timeout
  const serversPromise = getServers();
  const timeoutPromise = new Promise<{ error: string; status: number }>((resolve) => {
    setTimeout(() => {
      resolve({
        error: 'Request timed out. Please refresh the page to try again.',
        status: 408,
      });
    }, 10000); // 10 second timeout
  });

  const serversResult = await Promise.race([serversPromise, timeoutPromise]);

  let servers: ServerDataWithConnections[] = [];
  if (!('error' in serversResult)) {
    servers = serversResult.data;
  }

  // Get date for filtering recent activity
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  // Get user's hub IDs
  const userHubIds = userHubs.map((hub) => hub.id);

  // Get connections for user's hubs
  const connections = await db.connection.count({
    where: {
      hubId: { in: userHubIds },
      connected: true,
    },
  });

  // Get servers connected to user's hubs (unique count)
  const connectedServers = await db.connection.groupBy({
    by: ['serverId'],
    where: {
      hubId: { in: userHubIds },
      connected: true,
    },
  });

  // Get active hubs count (user's hubs with activity in the last 7 days)
  const activeHubs = userHubs.filter((hub) => new Date(hub.lastActive) >= oneWeekAgo).length;

  // Get total message count across user's hubs
  const totalMessages = userHubs.reduce((sum, hub) => {
    // We'll use connections as a proxy for message activity
    return sum + (hub.connections?.length || 0);
  }, 0);

  // Calculate average messages per day (rough estimate)
  const avgMessagesPerDay = Math.round(totalMessages / 7); // Assuming weekly activity

  const stats = {
    totalHubs: userHubs.length,
    totalConnections: connections,
    totalServers: connectedServers.length,
    activeHubs,
    messagesPerDay: avgMessagesPerDay,
  };

  // Filter hubs by permission level
  const ownedHubs = userHubs.filter((hub) => hub.permissionLevel === PermissionLevel.OWNER);
  const managedHubs = userHubs.filter((hub) => hub.permissionLevel === PermissionLevel.MANAGER);
  const moderatedHubs = userHubs.filter((hub) => hub.permissionLevel === PermissionLevel.MODERATOR);

  // Separate servers into those with and without the bot
  const serversWithBot = servers.filter((server) => server.botAdded);
  const serversWithoutBot = servers.filter((server) => !server.botAdded);
  const serverConnections = servers.flatMap((server) => server.connections);

  return (
    <div className="space-y-8">
      {/* Animated Welcome Hero */}
      <AnimatedWelcome user={session.user} />

      {/* Hub Connection Flow Banner */}
      {targetHub && (
        <Card className="border-indigo-500/50 bg-gradient-to-r from-indigo-900/20 to-purple-900/20 backdrop-blur-sm">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 rounded-full bg-indigo-500/20 flex items-center justify-center">
                <MessageSquare className="h-6 w-6 text-indigo-400" />
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-white">
                  Connect Server to {targetHub.name}
                </h3>
                <p className="text-gray-400 text-sm mt-1">
                  Choose a server below to connect to this hub, or switch to the &quot;My
                  Servers&quot; tab to get started.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Your Hubs"
          value={stats.totalHubs.toLocaleString()}
          description="Hubs you own or moderate"
          iconName="MessageSquare"
          index={0}
          color="purple"
        />
        <StatCard
          title="Connections"
          value={stats.totalConnections.toLocaleString()}
          description="Active channel connections"
          iconName="BarChart3"
          index={1}
          color="blue"
        />
        <StatCard
          title="Connected Servers"
          value={stats.totalServers.toLocaleString()}
          description="Discord servers in your hubs"
          iconName="Server"
          index={2}
          color="indigo"
        />
        <StatCard
          title="Active Hubs"
          value={stats.activeHubs.toLocaleString()}
          description="Hubs with recent activity"
          iconName="Activity"
          index={3}
          color="pink"
        />
      </div>

      {/* Main Dashboard Content with Tabs */}
      <div className="space-y-6">
        {/* Main Content - Servers and Hubs Tabs */}
        <UnderlinedTabs
          defaultValue={hubId ? 'servers' : 'servers'}
          className="w-full space-y-6"
          tabs={[
            {
              value: 'servers',
              label: 'My Servers',
              color: 'blue',
              icon: <Server className="h-4 w-4" />,
            },
            {
              value: 'hubs',
              label: 'My Hubs',
              color: 'purple',
              icon: <MessageSquare className="h-4 w-4" />,
            },
          ]}
        >
          <TabsContent value="servers" className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 pb-4">
              <div>
                <h2 className="text-2xl font-bold tracking-tight">My Servers</h2>
                <p className="text-gray-400 mt-1">
                  Manage your Discord servers connected to InterChat
                </p>
              </div>
              <Button
                asChild
                className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-none shadow-lg"
              >
                <Link
                  href="https://discord.com/oauth2/authorize?client_id=769921109209907241"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Add Bot to Server
                </Link>
              </Button>
            </div>

            {servers.length === 0 ? (
              <Card className="border-gray-800/50 bg-gradient-to-br from-gray-900/60 to-gray-950/60 backdrop-blur-sm">
                <CardHeader className="text-center py-12">
                  <div className="mx-auto w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center mb-4">
                    <Server className="h-6 w-6 text-blue-400" />
                  </div>
                  <CardTitle className="text-xl mb-2">No Servers Found</CardTitle>
                  <CardDescription className="text-gray-400 max-w-md mx-auto">
                    Add InterChat to your Discord servers to get started managing connections and
                    communities.
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center pb-12">
                  <Button
                    asChild
                    size="lg"
                    className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white border-none shadow-lg"
                  >
                    <Link
                      href="https://discord.com/oauth2/authorize?client_id=769921109209907241"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <PlusCircle className="h-4 w-4 mr-2" />
                      Add Bot to Server
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <UnderlinedTabs
                defaultValue="all"
                className="w-full space-y-4"
                tabs={[
                  {
                    value: 'all',
                    label: 'All Servers',
                    color: 'indigo',
                  },
                  {
                    value: 'connected',
                    label: 'Connected',
                    color: 'blue',
                  },
                  {
                    value: 'not-connected',
                    label: 'Not Connected',
                    color: 'purple',
                  },
                  {
                    value: 'connections',
                    label: 'Connections',
                    color: 'green',
                  },
                ]}
              >
                <TabsContent value="all" className="space-y-4">
                  <ServerGrid
                    servers={servers}
                    showConnectButton={!!targetHub}
                    selectedHubId={targetHub?.id}
                  />
                </TabsContent>
                <TabsContent value="connected" className="space-y-4">
                  {serversWithBot.length > 0 ? (
                    <ServerGrid
                      servers={serversWithBot}
                      showConnectButton={!!targetHub}
                      selectedHubId={targetHub?.id}
                    />
                  ) : (
                    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80">
                      <CardHeader>
                        <CardTitle>No Connected Servers</CardTitle>
                        <CardDescription>
                          You don&apos;t have any servers with InterChat added.
                        </CardDescription>
                      </CardHeader>
                    </Card>
                  )}
                </TabsContent>
                <TabsContent value="not-connected" className="space-y-4">
                  {serversWithoutBot.length > 0 ? (
                    <ServerGrid
                      servers={serversWithoutBot}
                      showConnectButton={!!targetHub}
                      selectedHubId={targetHub?.id}
                    />
                  ) : (
                    <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80">
                      <CardHeader>
                        <CardTitle>All Servers Connected</CardTitle>
                        <CardDescription>
                          All your servers already have InterChat added.
                        </CardDescription>
                      </CardHeader>
                    </Card>
                  )}
                </TabsContent>
                <TabsContent value="connections" className="space-y-4">
                  <ConnectionsGrid connections={serverConnections} />
                </TabsContent>
              </UnderlinedTabs>
            )}
          </TabsContent>

          <TabsContent value="hubs" className="space-y-6">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4 pb-4">
              <div>
                <h2 className="text-2xl font-bold tracking-tight">My Hubs</h2>
                <p className="text-gray-400 mt-1">Manage your InterChat hubs and connections</p>
              </div>
              <Button
                asChild
                className="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white border-none shadow-lg"
              >
                <Link href="/dashboard/hubs/create">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Create Hub
                </Link>
              </Button>
            </div>

            <UnderlinedTabs
              defaultValue="owned"
              className="space-y-4"
              tabs={[
                {
                  value: 'owned',
                  label: `Owned (${ownedHubs.length})`,
                  color: 'indigo',
                },
                {
                  value: 'managed',
                  label: `Managed (${managedHubs.length})`,
                  color: 'blue',
                },
                {
                  value: 'moderated',
                  label: `Moderated (${moderatedHubs.length})`,
                  color: 'purple',
                },
              ]}
            >
              <TabsContent value="owned" className="space-y-4">
                {ownedHubs.length === 0 ? (
                  <AnimatedEmptyState type="owned" />
                ) : (
                  <div className="grid gap-4 sm:gap-5 lg:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                    {ownedHubs.map((hub, index) => (
                      <AnimatedHubCard key={hub.id} hub={hub} index={index} />
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="managed" className="space-y-4">
                {managedHubs.length === 0 ? (
                  <AnimatedEmptyState type="managed" />
                ) : (
                  <div className="grid gap-4 sm:gap-5 lg:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                    {managedHubs.map((hub, index) => (
                      <AnimatedHubCard key={hub.id} hub={hub} index={index} />
                    ))}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="moderated" className="space-y-4">
                {moderatedHubs.length === 0 ? (
                  <AnimatedEmptyState type="moderated" />
                ) : (
                  <div className="grid gap-4 sm:gap-5 lg:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3">
                    {moderatedHubs.map((hub, index) => (
                      <AnimatedHubCard key={hub.id} hub={hub} index={index} />
                    ))}
                  </div>
                )}
              </TabsContent>
            </UnderlinedTabs>
          </TabsContent>
        </UnderlinedTabs>
        {/* Recent Notifications */}
        <RecentNotifications />
      </div>
    </div>
  );
}
