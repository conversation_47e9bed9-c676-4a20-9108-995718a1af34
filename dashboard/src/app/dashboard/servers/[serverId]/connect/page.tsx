'use client';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { ChannelIcon } from '@/components/discord/channel-icon';
import { ArrowLeft, Check, Hash, Home, Loader2, MessageSquare, Search, X } from 'lucide-react';
import { useSession } from 'next-auth/react';
import Image from 'next/image';
import Link from 'next/link';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

interface ServerData {
  id: string;
  name: string;
  icon: string | null;
}

interface HubData {
  id: string;
  name: string;
  iconUrl: string;
  description: string;
}

interface ChannelData {
  id: string;
  name: string;
  type: number;
  parentId: string | null;
  parentName: string | null;
  position: number;
  isThread: boolean;
  isPrivateThread: boolean;
}

export default function ServerConnectPage() {
  // Initialize with loading false to prevent unnecessary loading state
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isValidatingInvite, setIsValidatingInvite] = useState(false);
  const [inviteCode, setInviteCode] = useState('');
  const [isHubPreselected, setIsHubPreselected] = useState(false);
  const [preselectedHub, setPreselectedHub] = useState<HubData | null>(null);
  const searchParams = useSearchParams();
  const { serverId } = useParams();

  const [server, setServer] = useState<ServerData | null>(null);
  const [hubs, setHubs] = useState<HubData[]>([]);
  const [selectedHub, setSelectedHub] = useState<string>('');
  const [selectedChannel, setSelectedChannel] = useState<string>('');
  const [channels, setChannels] = useState<ChannelData[]>([]);
  const [channelSearchQuery, setChannelSearchQuery] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();
  const router = useRouter();
  const { data: session } = useSession();

  // Use refs to track data loading state to prevent duplicate fetches
  // This is crucial for preventing infinite loading when switching tabs
  const dataFetchedRef = useRef({
    server: false,
    hubs: false,
    channels: false,
  });

  // Check for hubId in query params (for redirects from public hubs)
  useEffect(() => {
    const hubIdParam = searchParams.get('hubId');

    if (hubIdParam) {
      // Fetch hub details and preselect it in
      const fetchHubDetails = async () => {
        try {
          const response = await fetch(`/api/hubs/${hubIdParam}`);

          if (!response.ok) {
            throw new Error('Failed to fetch hub details');
          }

          const data = await response.json();
          setSelectedHub(data.hub.id);
          setPreselectedHub(data.hub);
          setIsHubPreselected(true);
        } catch (error) {
          console.error('Error fetching hub details:', error);
          toast({
            title: 'Error',
            description: 'Failed to load hub details. Please try again.',
            variant: 'destructive',
          });
        }
      };

      fetchHubDetails();
    }
  }, [searchParams, toast]);

  // Fetch data when component mounts
  useEffect(() => {
    // Only proceed if we have a session
    if (!session) return;

    // Skip if we've already fetched all the data
    if (
      dataFetchedRef.current.server &&
      dataFetchedRef.current.hubs &&
      dataFetchedRef.current.channels
    ) {
      return;
    }

    let isMounted = true;

    const fetchAllData = async () => {
      // Only set loading state if we're actually going to fetch something
      if (
        !dataFetchedRef.current.server ||
        !dataFetchedRef.current.hubs ||
        !dataFetchedRef.current.channels
      ) {
        setIsLoading(true);
      }

      try {
        // Fetch server details if not already fetched
        if (!dataFetchedRef.current.server) {
          const serverResponse = await fetch(`/api/servers/${serverId}`);

          if (!serverResponse.ok) {
            throw new Error('Failed to fetch server data');
          }

          const serverData = await serverResponse.json();
          if (isMounted) {
            setServer(serverData.server);
            dataFetchedRef.current.server = true;
          }
        }

        // Fetch hubs the user has access to if not already fetched
        if (!dataFetchedRef.current.hubs) {
          const hubsResponse = await fetch('/api/hubs?moderated=true');

          if (!hubsResponse.ok) {
            throw new Error('Failed to fetch hubs data');
          }

          const hubsData = await hubsResponse.json();
          if (isMounted) {
            setHubs(hubsData.hubs);

            if (hubsData.hubs.length > 0 && !selectedHub && !isHubPreselected) {
              setSelectedHub(hubsData.hubs[0].id);
            }

            dataFetchedRef.current.hubs = true;
          }
        }

        // Fetch Discord channels if not already fetched
        if (!dataFetchedRef.current.channels) {
          // If we have a preselected hub, include the hubId in the request
          // to enable server-hub validation on the backend
          const hubIdParam = searchParams.get('hubId');
          const channelsUrl = hubIdParam
            ? `/api/discord/servers/${serverId}/channels?hubId=${hubIdParam}`
            : `/api/discord/servers/${serverId}/channels`;

          const channelsResponse = await fetch(channelsUrl);

          if (!channelsResponse.ok) {
            throw new Error('Failed to fetch Discord channels');
          }

          const channelsData = await channelsResponse.json();
          if (isMounted) {
            setChannels(channelsData.channels);
            dataFetchedRef.current.channels = true;

            // If no channels are returned and we have a hubId, it might be because
            // the server is already connected to this hub
            if (channelsData.channels.length === 0 && hubIdParam) {
              toast({
                title: 'Already Connected',
                description:
                  'This server is already connected to the selected hub. A server can only connect to a hub once.',
                variant: 'destructive',
              });
              router.push(`/dashboard/servers/${serverId}`);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        if (isMounted) {
          toast({
            title: 'Error',
            description: 'Failed to load server data. Please try again.',
            variant: 'destructive',
          });
          router.push(`/dashboard/servers/${serverId}`);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchAllData();

    return () => {
      isMounted = false;
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [session, serverId, toast, router]); // Intentionally omitting selectedHub and isHubPreselected to prevent re-fetching

  // Function to validate invite code
  const validateInviteCode = async () => {
    if (!inviteCode.trim()) return;

    try {
      setIsValidatingInvite(true);
      const response = await fetch(`/api/hubs/invite/${inviteCode}`);

      if (!response.ok) {
        const error = await response.json();

        // Handle specific blacklist errors
        if (error.error === 'You are blacklisted from this hub') {
          throw new Error(error.error);
        }

        throw new Error('Invalid invite code');
      }

      const data = await response.json();
      setSelectedHub(data.hub.id);
      setPreselectedHub(data.hub);
      setIsHubPreselected(true);

      toast({
        title: 'Success',
        description: `Joined hub: ${data.hub.name}`,
        variant: 'dashboard',
      });
    } catch (error) {
      console.error('Error validating invite code:', error);
      toast({
        title: 'Error',
        description:
          error instanceof Error && error.message === 'You are blacklisted from this hub'
            ? 'You are blacklisted from this hub'
            : 'Invalid or expired invite code',
        variant: 'destructive',
      });
    } finally {
      setIsValidatingInvite(false);
      setInviteCode('');
    }
  };

  // Filter hubs based on search query
  const filteredHubs = hubs.filter((hub) =>
    hub.name.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  // Filter channels based on search query
  const filteredChannels = channels.filter(
    (channel) =>
      channel.name.toLowerCase().includes(channelSearchQuery.toLowerCase()) ||
      (channel.parentName &&
        channel.parentName.toLowerCase().includes(channelSearchQuery.toLowerCase())),
  );

  const handleConnect = async () => {
    if (!selectedHub) {
      toast({
        title: 'Error',
        description: 'Please select a hub to connect to.',
        variant: 'destructive',
      });
      return;
    }

    // If we have a preselected hub, use its ID to ensure we're connecting to the right hub
    const hubIdToUse = isHubPreselected && preselectedHub ? preselectedHub.id : selectedHub;

    if (!selectedChannel) {
      toast({
        title: 'Error',
        description: 'Please select a channel for the connection.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const response = await fetch('/api/dashboard/connections', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          hubId: hubIdToUse,
          serverId: serverId,
          channelId: selectedChannel,
        }),
      });

      if (!response.ok) {
        const error = await response.json();

        // Handle specific blacklist errors
        if (
          error.error === 'You are blacklisted from this hub' ||
          error.error === 'This server is blacklisted from this hub'
        ) {
          throw new Error(error.error);
        }

        throw new Error(error.error || 'Failed to create connection');
      }

      toast({
        title: 'Connection Created',
        description: `The server has been successfully connected to ${
          preselectedHub?.name || 'the hub'
        }.`,
        variant: 'dashboard',
      });

      router.push(`/dashboard/servers/${serverId}`);
    } catch (error) {
      console.error('Error creating connection:', error);
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create connection',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[50vh]">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-gray-950">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              className="border-gray-700/50 bg-gray-800/50 hover:bg-gray-700/50 hover:text-white"
              asChild
            >
              <Link href={`/dashboard`}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Dashboard
              </Link>
            </Button>
            <div>
              <h1 className="text-3xl font-bold tracking-tight text-white">
                Connect Server to Hub
              </h1>
              <p className="text-gray-400 mt-1">
                {server?.name ? `Set up ${server.name} with a hub` : 'Connect your server to a hub'}
              </p>
            </div>
          </div>
          {server && (
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-full overflow-hidden">
                <Image
                  src={
                    server.icon
                      ? `https://cdn.discordapp.com/icons/${server.id}/${server.icon}.png?size=128`
                      : `https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(server.id)}`
                  }
                  alt={server.name}
                  width={40}
                  height={40}
                  className="object-cover"
                />
              </div>
              <div>
                <div className="text-sm font-medium text-white">{server.name}</div>
                <div className="text-xs text-gray-400">Server</div>
              </div>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Hub Selection */}
          <div className="lg:col-span-2">
            <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm">
              <CardHeader>
                <CardTitle className="text-xl font-semibold text-white">
                  {isHubPreselected ? 'Selected Hub' : 'Choose a Hub'}
                </CardTitle>
                <CardDescription className="text-gray-400">
                  {isHubPreselected
                    ? `This server will be connected to ${preselectedHub?.name}`
                    : 'Select which hub you want to connect this server to'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                {isHubPreselected ? (
                  <div className="space-y-6">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium text-white">Selected Hub</h3>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-400 hover:text-white hover:bg-gray-800/50"
                        onClick={() => {
                          setIsHubPreselected(false);
                          setPreselectedHub(null);
                        }}
                      >
                        <X className="h-4 w-4 mr-2" />
                        Change Hub
                      </Button>
                    </div>

                    <Card className="border-indigo-500/30 bg-gradient-to-r from-indigo-900/20 to-purple-900/20 backdrop-blur-sm">
                      <CardContent className="pt-6">
                        <div className="flex items-center gap-4">
                          <div className="relative">
                            <Image
                              src={
                                preselectedHub?.iconUrl ||
                                `https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(preselectedHub?.id || 'default')}`
                              }
                              alt={preselectedHub?.name || 'Hub'}
                              width={64}
                              height={64}
                              className="rounded-full ring-2 ring-indigo-500/30"
                            />
                            <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                              <Check className="h-3 w-3 text-white" />
                            </div>
                          </div>
                          <div>
                            <h4 className="text-xl font-bold text-white">{preselectedHub?.name}</h4>
                            <p className="text-sm text-gray-300 mt-1">{preselectedHub?.description}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <div className="flex items-center gap-2 text-sm text-green-400 bg-green-900/20 p-3 rounded-lg border border-green-500/20">
                      <Check className="h-4 w-4" />
                      <span>
                        Hub selected. Now choose a channel below to complete the connection.
                      </span>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="space-y-6">
                      <div className="bg-gray-800/30 p-4 rounded-lg border border-gray-700/50">
                        <Label
                          htmlFor="invite-code"
                          className="text-sm font-medium mb-2 block text-white flex items-center gap-2"
                        >
                          <MessageSquare className="h-4 w-4" />
                          Have an invite code?
                        </Label>
                        <div className="relative">
                          <Input
                            id="invite-code"
                            type="text"
                            placeholder="Enter invite code here..."
                            value={inviteCode}
                            onChange={(e) => setInviteCode(e.target.value)}
                            className="bg-gray-900/50 border-gray-600/50 pr-24 focus:border-indigo-500/50 focus:ring-indigo-500/20"
                          />
                          <Button
                            size="sm"
                            className="absolute right-1 top-1 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 border-none"
                            onClick={validateInviteCode}
                            disabled={isValidatingInvite || !inviteCode.trim()}
                          >
                            {isValidatingInvite ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Check className="h-4 w-4" />
                            )}
                          </Button>
                        </div>
                        <p className="text-xs text-gray-400 mt-2">
                          Use an invite code to join a private hub
                        </p>
                      </div>

                      <div className="relative">
                        <div className="absolute inset-0 flex items-center">
                          <span className="w-full border-t border-gray-700/50" />
                        </div>
                        <div className="relative flex justify-center text-xs uppercase">
                          <span className="bg-gray-900 px-2 text-gray-400">Or select from your hubs</span>
                        </div>
                      </div>

                      <div>
                        <div className="relative">
                          <Input
                            type="text"
                            placeholder="Search hubs..."
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            className="bg-gray-900/50 border-gray-600/50 pl-10 focus:border-indigo-500/50 focus:ring-indigo-500/20"
                          />
                          <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                            <Search className="h-4 w-4 text-gray-400" />
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3 max-h-[300px] overflow-y-auto pr-2 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                        {filteredHubs.length === 0 ? (
                          <div className="text-center py-8">
                            <div className="w-12 h-12 rounded-full bg-gray-800/50 flex items-center justify-center mx-auto mb-3">
                              <MessageSquare className="h-6 w-6 text-gray-400" />
                            </div>
                            <p className="text-gray-400">No hubs found matching your search.</p>
                            <p className="text-xs text-gray-500 mt-1">Try adjusting your search terms</p>
                          </div>
                        ) : (
                          filteredHubs.map((hub) => (
                            <div
                              key={hub.id}
                              className={`group p-4 rounded-lg cursor-pointer transition-all duration-200 ${
                                selectedHub === hub.id
                                  ? 'bg-gradient-to-r from-indigo-900/30 to-purple-900/30 border border-indigo-500/30 ring-1 ring-indigo-500/20'
                                  : 'bg-gray-800/30 border border-gray-700/50 hover:bg-gray-700/40 hover:border-gray-600/50'
                              }`}
                              onClick={() => {
                                setSelectedHub(hub.id);
                                setPreselectedHub(hub);
                                setIsHubPreselected(true);
                              }}
                            >
                              <div className="flex items-center gap-3">
                                <div className="relative">
                                  <Image
                                    src={
                                      hub.iconUrl ||
                                      `https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(hub.id)}`
                                    }
                                    alt={hub.name}
                                    width={48}
                                    height={48}
                                    className="rounded-full ring-2 ring-gray-600/50 group-hover:ring-gray-500/50 transition-all"
                                  />
                                  {selectedHub === hub.id && (
                                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-indigo-500 rounded-full flex items-center justify-center">
                                      <Check className="h-3 w-3 text-white" />
                                    </div>
                                  )}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="font-medium text-white group-hover:text-gray-100 transition-colors">
                                    {hub.name}
                                  </div>
                                  <div className="text-sm text-gray-400 line-clamp-2 group-hover:text-gray-300 transition-colors">
                                    {hub.description}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    </div>
                  </>
                )}

                <div className="space-y-4">
                  <Label htmlFor="channel" className="text-white font-medium flex items-center gap-2">
                    <Hash className="h-4 w-4" />
                    Discord Channel
                  </Label>
                  <div className="relative">
                    <Input
                      type="text"
                      placeholder="Search channels..."
                      value={channelSearchQuery}
                      onChange={(e) => setChannelSearchQuery(e.target.value)}
                      className="bg-gray-900/50 border-gray-600/50 pl-10 focus:border-indigo-500/50 focus:ring-indigo-500/20"
                    />
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      <Search className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>

                  <div className="max-h-[300px] overflow-y-auto space-y-2 pr-2 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                    {filteredChannels.length === 0 ? (
                      <div className="text-center py-8">
                        <div className="w-12 h-12 rounded-full bg-gray-800/50 flex items-center justify-center mx-auto mb-3">
                          <Hash className="h-6 w-6 text-gray-400" />
                        </div>
                        <p className="text-gray-400">No channels found</p>
                        <p className="text-xs text-gray-500 mt-1">Try adjusting your search terms</p>
                      </div>
                    ) : (
                      filteredChannels.map((channel) => (
                        <div
                          key={channel.id}
                          className={`group flex items-center p-3 rounded-lg cursor-pointer transition-all duration-200 ${
                            selectedChannel === channel.id
                              ? 'bg-gradient-to-r from-indigo-900/30 to-purple-900/30 border border-indigo-500/30 ring-1 ring-indigo-500/20'
                              : 'bg-gray-800/30 border border-gray-700/50 hover:bg-gray-700/40 hover:border-gray-600/50'
                          }`}
                          onClick={() => setSelectedChannel(channel.id)}
                        >
                          <ChannelIcon type={channel.type} className="h-4 w-4 mr-3 flex-shrink-0 text-gray-400 group-hover:text-gray-300" />
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate text-white group-hover:text-gray-100 transition-colors">{channel.name}</div>
                            {channel.parentName && (
                              <div className="text-xs text-gray-400 truncate group-hover:text-gray-300 transition-colors">
                                in {channel.parentName}
                              </div>
                            )}
                          </div>
                          {selectedChannel === channel.id && (
                            <div className="w-5 h-5 bg-indigo-500 rounded-full flex items-center justify-center ml-2">
                              <Check className="h-3 w-3 text-white" />
                            </div>
                          )}
                        </div>
                      ))
                    )}
                  </div>

                  <div className="bg-blue-900/20 p-3 rounded-lg border border-blue-500/20">
                    <p className="text-xs text-blue-300 flex items-start gap-2">
                      <MessageSquare className="h-3 w-3 mt-0.5 flex-shrink-0" />
                      Select the Discord channel where messages will be sent and received. This channel will be connected to the hub.
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="bg-gray-800/20 border-t border-gray-700/50">
                <Button
                  onClick={handleConnect}
                  disabled={isSubmitting || !selectedHub || !selectedChannel}
                  className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 disabled:from-gray-600 disabled:to-gray-700 border-none shadow-lg text-lg py-6 font-semibold"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-5 w-5 mr-3 animate-spin" />
                      Connecting Server...
                    </>
                  ) : (
                    <>
                      <MessageSquare className="h-5 w-5 mr-3" />
                      Connect to Hub
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>

          {/* Right Column - Server Info */}
          <div>
            <Card className="border border-gray-800/50 bg-gradient-to-b from-gray-900/80 to-gray-950/80 backdrop-blur-sm sticky top-8">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Home className="h-5 w-5" />
                  Server Information
                </CardTitle>
                <CardDescription>The Discord server you&apos;re connecting</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center gap-4 p-4 bg-gray-800/30 rounded-lg border border-gray-700/50">
                  <div className="relative">
                    <Image
                      src={
                        server?.icon
                          ? `https://cdn.discordapp.com/icons/${server.id}/${server.icon}.png?size=128`
                          : `https://api.dicebear.com/7.x/identicon/svg?seed=${encodeURIComponent(server?.id || (serverId as string))}`
                      }
                      alt={server?.name || 'Server'}
                      width={64}
                      height={64}
                      className="rounded-full ring-2 ring-gray-600/50"
                    />
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                  </div>
                  <div>
                    <div className="font-semibold text-lg text-white">
                      {server?.name || 'Unknown Server'}
                    </div>
                    <div className="flex items-center text-sm text-gray-400">
                      <Home className="h-3 w-3 mr-1" />
                      Discord Server
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h3 className="text-sm font-semibold text-white flex items-center gap-2">
                    <MessageSquare className="h-4 w-4" />
                    About Hub Connections
                  </h3>
                  <div className="bg-indigo-900/20 p-4 rounded-lg border border-indigo-500/20">
                    <p className="text-sm text-indigo-200 mb-3">
                      Connecting your server to a hub allows members to chat across Discord servers.
                    </p>
                    <ul className="text-sm text-indigo-300 space-y-2">
                      <li className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-indigo-400 rounded-full mt-2 flex-shrink-0"></div>
                        Messages sent in the selected channel will be shared with the hub
                      </li>
                      <li className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-indigo-400 rounded-full mt-2 flex-shrink-0"></div>
                        Messages from other servers in the hub will appear in your channel
                      </li>
                      <li className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-indigo-400 rounded-full mt-2 flex-shrink-0"></div>
                        You can disconnect at any time from the server management page
                      </li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
