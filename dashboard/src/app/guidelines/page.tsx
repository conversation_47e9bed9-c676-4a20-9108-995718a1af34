'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  BadgeAlert,
  Ban,
  Gavel,
  MessageSquareWarning,
  Scale,
  Shield,
  ShieldAlert,
} from 'lucide-react';

const InterChatRules = () => {
  const rules = [
    {
      id: 1,
      title: 'Hate Speech & Harassment',
      icon: <Scale className="w-6 h-6 text-rose-500" />,
      notAllowed: [
        'Using slurs or hate speech to attack others',
        'Harassing or threatening users',
        'Naming a hub with offensive or hateful language',
      ],
      warning:
        'Hubs that allow casual slur usage are not automatically banned, but if hate speech is reported and ignored, the hub may face action.',
    },
    {
      id: 2,
      title: 'Illegal Content',
      icon: <Gavel className="w-6 h-6 text-amber-500" />,
      notAllowed: [
        'Sharing links to illegal content (e.g., CSAM, hacking tools)',
        'Encouraging violence, self-harm, or criminal activity',
        'Doxxing (posting private info like addresses)',
      ],
      warning:
        "Discussions about laws, crime, or mental health are allowed as long as they don't promote harm.",
    },
    {
      id: 3,
      title: 'Severe NSFW & Gore',
      icon: <Ban className="w-6 h-6 text-rose-500" />,
      notAllowed: [
        'Posting gore or extreme gore in InterChat',
        'Posting sexual content in non-NSFW hubs',
        'Running an NSFW hub without properly labeling it',
      ],
      warning: 'Mild NSFW jokes and discussions are fine in appropriate spaces that allow it.',
    },
    {
      id: 4,
      title: 'Severe Spam & Raiding',
      icon: <MessageSquareWarning className="w-6 h-6 text-orange-500" />,
      notAllowed: [
        'Mass spamming or bot floods',
        'Organizing raids on other hubs',
        'Scam links and phishing',
      ],
      warning: "Fast-moving chats are fine if they're natural conversations.",
    },
    {
      id: 5,
      title: 'Impersonation & Fraud',
      icon: <BadgeAlert className="w-6 h-6 text-red-500" />,
      notAllowed: [
        'Impersonating InterChat staff or hub moderators',
        'Creating fake hubs pretending to be official communities',
        'Running cryptocurrency or NFT scams',
        'Selling or trading accounts/hubs',
      ],
      warning:
        'Discussions about cryptocurrencies and NFTs are allowed, but organizing trades or sales is prohibited.',
    },
    {
      id: 6,
      title: 'Exploitation & Abuse',
      icon: <ShieldAlert className="w-6 h-6 text-rose-500" />,
      notAllowed: [
        'Grooming or predatory behavior towards minors',
        'Sharing or requesting personal information of others',
        'Blackmailing or threatening to expose private details',
        'Creating hubs dedicated to harassing specific individuals',
      ],
      warning:
        'Any content involving the exploitation of minors will result in immediate permanent ban and Discord being notified.',
    },
    {
      id: 7,
      title: 'Malicious Software',
      icon: <Shield className="w-6 h-6 text-amber-500" />,
      notAllowed: [
        'Sharing malware, viruses, or harmful scripts',
        'Distributing token grabbers or account stealers',
        "Promoting fake 'free nitro' or similar scams",
        'Sharing tools designed to harm Discord servers',
      ],
      warning:
        'Discussing cybersecurity topics is allowed, but sharing harmful tools or exploits is strictly prohibited.',
    },
  ];

  return (
    <section className="w-full bg-gradient-to-br from-gray-50 via-purple-50/20 to-gray-100 dark:from-gray-900 dark:via-purple-900/10 dark:to-gray-800 min-h-screen">
      <main className="container mx-auto pt-28 pb-20 px-4 relative">
        {/* Animated decorative elements */}
        <div className="absolute top-20 left-1/4 w-60 h-60 bg-purple-600 opacity-5 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-1/4 w-80 h-80 bg-purple-600 opacity-5 rounded-full blur-xl"></div>
        <div className="absolute top-40 right-1/3 w-40 h-40 bg-purple-500 opacity-5 rounded-full blur-xl"></div>

        <div className="max-w-4xl mx-auto relative z-10">
          <div className="text-center mb-12">
            <div className="inline-block mb-4">
              <div className="flex items-center justify-center bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-5 py-2 rounded-full border border-purple-200 dark:border-purple-700/40 shadow-sm">
                <ShieldAlert className="w-5 h-5 mr-2" />
                <span className="font-medium">Base Rules</span>
              </div>
            </div>

            <h1 className="text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-blue-500 dark:from-purple-400 dark:to-blue-300 mb-6">
              Community Guidelines
            </h1>

            <p className="text-lg mb-12 dark:text-gray-300 text-gray-600 max-w-2xl mx-auto">
              To keep InterChat safe and manageable, all hubs and users must follow these
              <span className="font-bold"> absolute rules</span>. Violating them can result in
              <span className="font-bold"> warnings, suspensions, or permanent bans</span> from
              InterChat.
            </p>
          </div>

          <div className="space-y-6">
            {rules.map((rule) => (
              <div
                key={rule.id}
                className="bg-white/40 dark:bg-white/5 backdrop-blur-xl rounded-xl border border-purple-100 dark:border-primary/20 shadow-lg overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-center gap-4 mb-4">
                    <div className="p-3 rounded-lg bg-purple-100 dark:bg-primary/10 flex-shrink-0">
                      {rule.icon}
                    </div>
                    <h2 className="text-xl font-semibold dark:text-zinc-100 text-zinc-700">
                      {rule.id}. {rule.title}
                    </h2>
                  </div>

                  <div className="pl-16">
                    <div className="mb-4">
                      <p className="font-semibold text-rose-600 dark:text-rose-400 mb-2 flex items-center">
                        <Ban className="w-4 h-4 mr-2" /> Not Allowed:
                      </p>
                      <ul className="space-y-2 pl-6 list-disc marker:text-rose-400">
                        {rule.notAllowed.map((item, i) => (
                          <li key={i} className="dark:text-zinc-300 text-zinc-600">
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="bg-amber-50 dark:bg-amber-950/30 p-3 rounded-lg border border-amber-200 dark:border-amber-800/50">
                      <p className="text-amber-800 dark:text-amber-300 flex items-center text-sm">
                        <AlertTriangle className="w-4 h-4 mr-2 flex-shrink-0" />
                        {rule.warning}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Line to separate sections */}
            <div className="h-px w-full bg-gray-200 dark:bg-gray-700 my-12"></div>

            {/* Enforcement Section */}
            <div className="bg-white/40 dark:bg-white/5 backdrop-blur-xl rounded-xl border border-purple-300 dark:border-primary/30 shadow-lg overflow-hidden mt-12">
              <div className="p-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-3 rounded-lg bg-purple-100 dark:bg-primary/10 flex-shrink-0">
                    <BadgeAlert className="w-6 h-6 text-purple-700 dark:text-purple-300" />
                  </div>
                  <h2 className="text-xl font-semibold dark:text-zinc-100 text-zinc-700">
                    How Are Violations Enforced?
                  </h2>
                </div>

                <div className="pl-16">
                  <ul className="space-y-3">
                    <li className="dark:text-zinc-300 text-zinc-600 flex items-center gap-2">
                      <span className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-100 dark:bg-primary/10 flex items-center justify-center text-purple-700 dark:text-purple-300 text-sm font-medium">
                        1
                      </span>
                      <span>
                        <strong>User Reports First:</strong> If a hub/user is reported, we
                        investigate.
                      </span>
                    </li>
                    <li className="dark:text-zinc-300 text-zinc-600 flex items-center gap-2">
                      <span className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-100 dark:bg-primary/10 flex items-center justify-center text-purple-700 dark:text-purple-300 text-sm font-medium">
                        2
                      </span>
                      <span>
                        <strong>Warnings Before Suspensions:</strong> Unless it&apos;s extremely
                        serious.
                      </span>
                    </li>
                    <li className="dark:text-zinc-300 text-zinc-600 flex items-center gap-2">
                      <span className="flex-shrink-0 w-6 h-6 rounded-full bg-purple-100 dark:bg-primary/10 flex items-center justify-center text-purple-700 dark:text-purple-300 text-sm font-medium">
                        3
                      </span>
                      <span>
                        <strong>Repeat Offenses:</strong> Lead to hub/user bans.
                      </span>
                    </li>
                  </ul>

                  <div className="mt-4 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg border border-purple-200 dark:border-purple-800/30">
                    <p className="text-purple-800 dark:text-purple-200 text-sm flex items-center">
                      <MessageSquareWarning className="w-4 h-4 mr-2 flex-shrink-0" />
                      <span className="font-semibold">
                        InterChat allows hubs to have their own rules
                      </span>
                      , but violations of these base rules will result in action.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-12 p-6 bg-amber-50/50 dark:bg-amber-900/10 border border-amber-200 dark:border-amber-700/30 rounded-xl">
            <div className="flex items-center gap-3 mb-3">
              <AlertTriangle className="w-5 h-5 text-amber-600 dark:text-amber-500" />
              <h3 className="text-lg font-semibold text-amber-800 dark:text-amber-400">
                Additional Considerations
              </h3>
            </div>
            <p className="text-amber-800/90 dark:text-amber-300/90 text-base">
              The rules and examples listed above are not exhaustive. InterChat moderators reserve
              the right to take action against any behavior that threatens the safety, integrity, or
              well-being of our community, even if not explicitly listed here. We encourage users to
              exercise good judgment and maintain a respectful environment.
            </p>
          </div>

          <div className="mt-12 pt-8 border-t dark:border-gray-700/50 border-gray-200">
            <div className="flex flex-col md:flex-row items-center justify-center gap-6 text-center">
              <p className="dark:text-gray-400 text-gray-600">
                These rules are enforced for everyone&apos;s safety
              </p>
              <a
                href="/support"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center px-4 py-2 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full border border-purple-200 dark:border-purple-700/40 hover:bg-purple-200 dark:hover:bg-purple-800/40 transition-colors duration-200"
              >
                <Shield className="w-4 h-4 mr-2" />
                <span className="font-medium">Report a Violation</span>
              </a>
            </div>
          </div>
        </div>
      </main>
    </section>
  );
};

export default InterChatRules;
