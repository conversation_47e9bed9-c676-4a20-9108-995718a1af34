import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    ArrowRight,
    CheckCircle2,
    Globe,
    Lock,
    Search,
    Settings,
    Shield,
    Zap
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface Feature {
  title: string;
  description: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  benefits?: string[];
  imageSrc?: string;
  imageAlt?: string;
  learnMoreLink?: string;
  layout?: "left" | "right" | "center";
}

const FEATURES: Feature[] = [
  {
    title: "Connect Discord Servers Instantly",
    description:
      "Link channels across different Discord servers so people can chat together, no matter which server they're in. Messages appear in real-time across all connected channels.",
    icon: Globe,
    benefits: [
      "Chat with people from other Discord servers",
      "Messages sync instantly across all connections",
      "Works with any text channel - no special setup needed",
      "Connect unlimited servers to your hub",
    ],
    imageSrc: "/features/cross-server-chat.png",
    imageAlt: "Active chat flowing between multiple connected Discord servers",
    learnMoreLink: "/docs/hub/creating-hub",
    layout: "left",
  },
  {
    title: "Discover Active Communities",
    description:
      "Find thriving Discord servers that match your interests using our intuitive discovery system. Browse by category, activity level, or search for specific topics.",
    icon: Search,
    benefits: [
      "Browse thousands of active Discord communities",
      "Filter by interests, language, and activity level",
      "Preview communities before joining",
      "Get recommendations based on your preferences",
    ],
    imageSrc: "/features/HubDiscovery.png",
    imageAlt:
      "InterChat interface for discovering and joining shared community hubs",
    learnMoreLink: "/hubs",
    layout: "right",
  },
  {
    title: "Built-in Safety & Moderation",
    description:
      "Keep your communities safe with AI-powered moderation that automatically detects and handles unwanted content, spam, and inappropriate behavior.",
    icon: Shield,
    benefits: [
      "AI detects NSFW content and spam automatically",
      "Customizable word filters and content rules",
      "Real-time moderation across all connected servers",
      "Detailed moderation logs and analytics",
    ],
    imageSrc: "/features/NSFWDetection.svg",
    imageAlt: "InterChat AI moderation tools protecting Discord servers",
    learnMoreLink: "/docs/guides/moderation",
    layout: "left",
  },
  {
    title: "Private & Secure Connections",
    description:
      "Create invite-only hubs for your inner circle, partner servers, or exclusive communities. Full control over who can join and participate.",
    icon: Lock,
    benefits: [
      "Invite-only hubs for exclusive communities",
      "Granular permission controls",
      "Audit logs for all activities",
    ],
    learnMoreLink: "/docs/getting-started",
    layout: "right",
  },
  {
    title: "Advanced Customization",
    description:
      "Personalize your hub experience with custom welcome messages, rules, webhook appearances, and automated responses for new members.",
    icon: Settings,
    benefits: [
      "Custom webhook names and avatars",
      "Automated welcome messages and rules",
      "Branded message formatting",
      "Custom emoji and reaction syncing",
    ],
    imageSrc: "/features/HubSettings.svg",
    imageAlt: "InterChat hub settings page with customization options",
    learnMoreLink: "/docs/guides/hub-management",
    layout: "left",
  },
];

const FeatureCard = ({
  feature,
}: {
  feature: Feature;
  index: number;
}) => {
  const isImageLeft = feature.layout === "left";
  const hasImage = !!feature.imageSrc;

  return (
    <div className="mb-24 last:mb-0">
      <div
        className={`grid grid-cols-1 ${
          hasImage ? "lg:grid-cols-2" : "lg:grid-cols-1"
        } gap-12 lg:gap-16 items-center max-w-7xl mx-auto`}
      >
        {/* Content */}
        <div
          className={`space-y-6 ${
            hasImage && !isImageLeft ? "lg:order-2" : ""
          } ${!hasImage ? "max-w-4xl mx-auto text-center" : ""}`}
        >
          <div className="space-y-4">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-3 rounded-xl bg-gradient-to-br from-blue-600/20 to-purple-600/20 border border-blue-500/30">
                <feature.icon className="w-8 h-8 text-blue-400" />
              </div>
              <h3 className="text-3xl md:text-4xl font-bold text-white leading-tight">
                {feature.title}
              </h3>
            </div>

            <p className="text-xl text-gray-300 leading-relaxed">
              {feature.description}
            </p>
          </div>

          {/* Benefits */}
          {feature.benefits && feature.benefits.length > 0 && (
            <div className="space-y-3">
              <h4 className="text-lg font-semibold text-white">
                Key Benefits:
              </h4>
              <ul className="space-y-3">
                {feature.benefits.map((benefit) => (
                  <li
                    key={benefit}
                    className="flex items-start gap-3 text-gray-300"
                  >
                    <CheckCircle2 className="w-6 h-6 text-green-400 flex-shrink-0 mt-0.5" />
                    <span className="text-lg">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Learn More Button */}
          {feature.learnMoreLink && (
            <div className="pt-4">
              <Button
                asChild
                size="lg"
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3"
              >
                <Link href={feature.learnMoreLink}>
                  Learn More
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Link>
              </Button>
            </div>
          )}
        </div>

        {/* Image */}
        {hasImage && (
          <div className={`${!isImageLeft ? "lg:order-1" : ""}`}>
            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden bg-gradient-to-br from-gray-800/50 to-gray-900/50 border border-gray-700/50 shadow-2xl">
                <div className="aspect-[4/3] relative">
                  <Image
                    // FIXME: ahhhh
                    src={feature.imageSrc ?? ""}
                    alt={feature.imageAlt || feature.title}
                    fill
                    className="object-cover"
                    sizes="(max-width: 768px) 100vw, 50vw"
                    loading="lazy"
                    placeholder="blur"
                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkbHB0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
                  />
                </div>
                {/* Overlay gradient for better text readability */}
                <div className="absolute inset-0 bg-gradient-to-t from-gray-900/20 to-transparent" />
              </div>

              {/* Decorative elements */}
              <div className="absolute -inset-4 bg-gradient-to-r from-blue-600/10 to-purple-600/10 rounded-3xl blur-xl -z-10" />
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export function SpacedFeatures() {
  // Show all features statically
  const visibleFeatures = FEATURES;

  return (
    <section className="py-24 md:py-32 bg-gradient-to-b from-gray-900 via-gray-950 to-gray-900">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 bg-gray-800/50 text-white px-6 py-3 rounded-full border border-gray-700/50 mb-8">
            <Zap className="w-5 h-5 text-blue-400" />
            <span className="font-medium text-lg">Powerful Features</span>
          </div>

          <h2 className="text-4xl md:text-6xl lg:text-7xl font-bold mb-8 text-white leading-tight">
            Everything You Need to
            <span className="block bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
              Connect Communities
            </span>
          </h2>

          <p className="max-w-4xl mx-auto text-xl md:text-2xl text-gray-300 leading-relaxed">
            Simple, powerful features that make connecting Discord servers easy
            and fun for everyone.
          </p>
        </div>

        {/* Features */}
        <div className="space-y-0">
          {visibleFeatures.map((feature, index) => (
            <FeatureCard key={feature.title} feature={feature} index={index} />
          ))}
        </div>
      </div>
    </section>
  );
}
