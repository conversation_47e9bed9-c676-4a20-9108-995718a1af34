'use client';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';
import {
  AlertTriangle,
  Bell,
  ChevronDown,
  Edit,
  FileText,
  Home,
  Menu,
  MessageSquare,
  ScrollText,
  Settings,
  Shield,
  Users,
} from 'lucide-react';
import { motion } from 'motion/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface HubMobileDropdownProps {
  hubId: string;
  canModerate?: boolean;
  canEdit?: boolean;
}

// Color mapping for navigation items
const colorMap = {
  indigo: 'text-indigo-400 hover:text-indigo-300',
  blue: 'text-blue-400 hover:text-blue-300',
  green: 'text-green-400 hover:text-green-300',
  purple: 'text-purple-400 hover:text-purple-300',
  red: 'text-red-400 hover:text-red-300',
  orange: 'text-orange-400 hover:text-orange-300',
  pink: 'text-pink-400 hover:text-pink-300',
} as const;

interface DropdownNavItemProps {
  href: string;
  icon: React.ElementType;
  label: string;
  active: boolean;
  color: keyof typeof colorMap;
}

function DropdownNavItem({ href, icon: Icon, label, active, color }: DropdownNavItemProps) {
  return (
    <DropdownMenuItem asChild>
      <Link
        href={href}
        className={cn(
          'flex items-center gap-3 px-3 py-2.5 rounded-lg transition-all duration-200 cursor-pointer',
          'hover:bg-gray-800/60 focus:bg-gray-800/60',
          active
            ? `${colorMap[color]} bg-gray-800/40 font-medium`
            : 'text-gray-300 hover:text-white',
        )}
      >
        <Icon className={cn('h-4 w-4', active ? colorMap[color] : 'text-gray-400')} />
        <span className="text-sm">{label}</span>
      </Link>
    </DropdownMenuItem>
  );
}

export function HubMobileDropdown({
  hubId,
  canModerate = false,
  canEdit = false,
}: HubMobileDropdownProps) {
  const pathname = usePathname();

  // Define all navigation items with their permissions
  const navigationItems = [
    {
      value: 'overview',
      label: 'Overview',
      color: 'indigo' as const,
      icon: MessageSquare,
      href: `/dashboard/hubs/${hubId}`,
      section: 'Main',
      show: true,
    },
    {
      value: 'edit',
      label: 'Edit Hub',
      color: 'blue' as const,
      icon: Edit,
      href: `/dashboard/hubs/${hubId}/edit`,
      section: 'Management',
      show: canEdit,
    },
    {
      value: 'members',
      label: 'Members',
      color: 'blue' as const,
      icon: Users,
      href: `/dashboard/hubs/${hubId}/members`,
      section: 'Management',
      show: canModerate,
    },
    {
      value: 'connections',
      label: 'Connections',
      color: 'green' as const,
      icon: Home,
      href: `/dashboard/hubs/${hubId}/connections`,
      section: 'Management',
      show: canModerate,
    },
    {
      value: 'logging',
      label: 'Logging',
      color: 'purple' as const,
      icon: FileText,
      href: `/dashboard/hubs/${hubId}/logging`,
      section: 'Management',
      show: canEdit,
    },
    {
      value: 'moderation',
      label: 'Moderation',
      color: 'purple' as const,
      icon: Shield,
      href: `/dashboard/hubs/${hubId}/moderation`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'reports',
      label: 'Reports',
      color: 'purple' as const,
      icon: Shield,
      href: `/dashboard/hubs/${hubId}/reports`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'appeals',
      label: 'Appeals',
      color: 'pink' as const,
      icon: Bell,
      href: `/dashboard/hubs/${hubId}/appeals`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'moderation-logs',
      label: 'Audit Logs',
      color: 'blue' as const,
      icon: ScrollText,
      href: `/dashboard/hubs/${hubId}/moderation-logs`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'infractions',
      label: 'Infractions',
      color: 'orange' as const,
      icon: AlertTriangle,
      href: `/dashboard/hubs/${hubId}/infractions`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'anti-swear',
      label: 'Anti-Swear',
      color: 'red' as const,
      icon: AlertTriangle,
      href: `/dashboard/hubs/${hubId}/anti-swear`,
      section: 'Moderation',
      show: canModerate,
    },
    {
      value: 'settings',
      label: 'Settings',
      color: 'orange' as const,
      icon: Settings,
      href: `/dashboard/hubs/${hubId}/settings`,
      section: 'Settings',
      show: true,
    },
  ];

  // Filter items based on permissions and group by section
  const visibleItems = navigationItems.filter((item) => item.show);
  const sections = ['Main', 'Management', 'Moderation', 'Settings'];
  const groupedItems = sections.reduce(
    (acc, section) => {
      const items = visibleItems.filter((item) => item.section === section);
      if (items.length > 0) {
        acc[section] = items;
      }
      return acc;
    },
    {} as Record<string, typeof visibleItems>,
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <motion.div
          initial={{ scale: 0.95, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.3, ease: 'easeOut' }}
        >
          <Button className="w-full justify-between gap-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-500 hover:to-purple-500 border-0 text-white font-medium py-3 px-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 group relative overflow-hidden">
            {/* Background animation */}
            <div className="absolute inset-0 bg-gradient-to-r from-indigo-400/20 to-purple-400/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

            <div className="flex items-center gap-3 relative z-10">
              <motion.div
                animate={{ rotate: [0, 5, -5, 0] }}
                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
              >
                <Menu className="h-5 w-5" />
              </motion.div>
              <span className="text-base">Hub Navigation</span>
            </div>

            <ChevronDown className="h-5 w-5 text-white group-hover:text-white/70 transition-colors duration-300" />
          </Button>
        </motion.div>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="center"
        className="w-80 max-h-[70vh] overflow-y-auto bg-gray-900/95 backdrop-blur-md border-gray-800/50 text-gray-100 shadow-2xl rounded-xl p-2"
        sideOffset={8}
      >
        <DropdownMenuLabel className="px-3 py-2 text-lg font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">
          Hub Navigation
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-gray-800/50 my-2" />

        {Object.entries(groupedItems).map(([section, items], sectionIndex) => (
          <div key={section}>
            {sectionIndex > 0 && <DropdownMenuSeparator className="bg-gray-800/30 my-2" />}

            <DropdownMenuGroup>
              <DropdownMenuLabel className="px-3 py-1 text-xs font-medium text-gray-400 uppercase tracking-wider">
                {section}
              </DropdownMenuLabel>
              {items.map((item) => (
                <DropdownNavItem
                  key={item.value}
                  href={item.href}
                  icon={item.icon}
                  label={item.label}
                  active={pathname === item.href}
                  color={item.color}
                />
              ))}
            </DropdownMenuGroup>
          </div>
        ))}

        <DropdownMenuSeparator className="bg-gray-800/30 my-2" />
        <div className="px-3 py-2">
          <p className="text-xs text-gray-500 text-center flex items-center justify-center gap-1">
            <span>💡</span> Quick access to all hub features
          </p>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
