"use client";

import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { AnimatePresence, motion } from "motion/react";
import {
  Bell,
  Home,
  MessageCircle,
  MessageSquare,
  Settings,
  Shield,
  Users,
  X
} from "lucide-react";
import type { User } from "next-auth";
import { signOut } from "next-auth/react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { createPortal } from "react-dom";

interface MobileSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  user: User;
}

export function MobileSidebar({ isOpen, onClose, user }: MobileSidebarProps) {
  const [mounted, setMounted] = useState(false);

  // Get user initials for avatar fallback
  const getInitials = (name: string | null | undefined) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Only mount the portal on the client side
  useEffect(() => {
    setMounted(true);

    // Prevent body scrolling when sidebar is open
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }

    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  if (!mounted) return null;

  // Use createPortal to render the sidebar at the document body level
  // This ensures it's not constrained by any parent containers
  return createPortal(
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Overlay - covers the entire viewport */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="fixed inset-0 bg-black/80 backdrop-blur-sm z-[9998]"
            onClick={onClose}
            style={{ position: "fixed", top: 0, left: 0, right: 0, bottom: 0 }}
          />

          {/* Sidebar - positioned relative to the viewport */}
          <motion.div
            initial={{ x: "-100%" }}
            animate={{ x: 0 }}
            exit={{ x: "-100%" }}
            transition={{ duration: 0.3, ease: "easeOut" }}
            className="fixed top-0 left-0 bottom-0 w-[280px] max-w-[80vw] flex flex-col bg-gradient-to-b from-gray-900 to-gray-950 border-r border-gray-800/50 shadow-xl overflow-hidden z-[9999]"
            style={{ position: "fixed", top: 0, left: 0, bottom: 0 }}
          >
            {/* Mobile sidebar header */}
            <div className="h-16 flex items-center justify-between px-4 border-b border-gray-800/50">
              <Link
                href="/dashboard"
                className="flex items-center gap-2"
                onClick={onClose}
              >
                <span className="font-bold text-lg bg-clip-text text-transparent bg-gradient-to-r from-indigo-400 to-purple-400">
                  InterChat
                </span>
              </Link>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="text-gray-400 hover:text-white rounded-[var(--radius-avatar)] h-8 w-8 transition-all duration-200"
              >
                <X className="h-5 w-5" />
                <span className="sr-only">Close sidebar</span>
              </Button>
            </div>

            {/* Mobile menu items */}
            <div className="flex-1 overflow-y-auto py-4 px-3">
              <div className="space-y-1">
                <Link
                  href="/dashboard"
                  className="flex items-center gap-3 rounded-[var(--radius-button)] px-3 py-2 text-sm text-gray-200 hover:text-white hover:bg-gray-800/50 transition-all duration-200"
                  onClick={onClose}
                >
                  <Home className="h-5 w-5 text-indigo-400" />
                  <span>Dashboard</span>
                </Link>

                <div className="pt-4 pb-2">
                  <div className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                    Hub Management
                  </div>
                </div>

                <Link
                  href="/dashboard"
                  className="flex items-center gap-3 rounded-[var(--radius-button)] px-3 py-2 text-sm text-gray-200 hover:text-white hover:bg-gray-800/50 transition-all duration-200"
                  onClick={onClose}
                >
                  <MessageSquare className="h-5 w-5 text-indigo-400" />
                  <span>My Hubs</span>
                </Link>

                <Link
                  href="/dashboard"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-gray-200 hover:text-white hover:bg-gray-800/50"
                  onClick={onClose}
                >
                  <Home className="h-5 w-5 text-blue-400" />
                  <span>My Servers</span>
                </Link>

                <Link
                  href="/dashboard/my-appeals"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-gray-200 hover:text-white hover:bg-gray-800/50"
                  onClick={onClose}
                >
                  <MessageCircle className="h-5 w-5 text-green-400" />
                  <span>My Appeals</span>
                </Link>

                <div className="pt-4 pb-2">
                  <div className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                    Moderation
                  </div>
                </div>

                <Link
                  href="/dashboard/moderation/reports"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-gray-200 hover:text-white hover:bg-gray-800/50"
                  onClick={onClose}
                >
                  <Shield className="h-5 w-5 text-purple-400" />
                  <span>Reports</span>
                </Link>

                <Link
                  href="/dashboard/moderation/blacklist"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-gray-200 hover:text-white hover:bg-gray-800/50"
                  onClick={onClose}
                >
                  <Users className="h-5 w-5 text-red-400" />
                  <span>Blacklists</span>
                </Link>

                <Link
                  href="/dashboard/appeals"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-gray-200 hover:text-white hover:bg-gray-800/50"
                  onClick={onClose}
                >
                  <Bell className="h-5 w-5 text-yellow-400" />
                  <span>Appeals</span>
                </Link>

                <div className="pt-4 pb-2">
                  <div className="px-3 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                    Settings
                  </div>
                </div>

                <Link
                  href="/dashboard/settings"
                  className="flex items-center gap-3 rounded-lg px-3 py-2 text-sm text-gray-200 hover:text-white hover:bg-gray-800/50"
                  onClick={onClose}
                >
                  <Settings className="h-5 w-5 text-gray-400" />
                  <span>Settings</span>
                </Link>
              </div>
            </div>

            {/* Mobile user section */}
            <div className="border-t border-gray-800/50 p-4">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10 border-2 border-indigo-500/20">
                  <AvatarImage
                    src={user.image || undefined}
                    alt={user.name || "User"}
                  />
                  <AvatarFallback className="bg-gradient-to-br from-indigo-500/80 to-purple-500/80 text-white">
                    {getInitials(user.name)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <p className="text-sm font-medium text-white">{user.name}</p>
                  <Button
                    onClick={() => {
                      onClose();
                      signOut({ callbackUrl: "/" });
                    }}
                    className="text-xs text-red-400 hover:text-red-300"
                  >
                    Sign out
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>,
    document.body,
  );
}
