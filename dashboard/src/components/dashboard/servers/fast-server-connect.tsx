'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Home, MessageSquare } from 'lucide-react';
import Link from 'next/link';

interface FastServerConnectProps {
  serverId: string;
  serverName: string;
  serverIcon: string | null;
  botAdded: boolean;
}

export function FastServerConnect({ serverId, botAdded }: FastServerConnectProps) {
  // If bot is not added, show a different UI
  if (!botAdded) {
    return (
      <Card className="border border-yellow-500/20 bg-yellow-950/10">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Home className="h-5 w-5 text-yellow-400" />
            Quick Connect
          </CardTitle>
          <CardDescription>Add the bot to this server first</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-400 mb-4">
            You need to add the InterChat bot to this server before you can connect it to a hub.
          </p>
        </CardContent>
        <CardFooter>
          <Button
            className="cursor-pointer w-full bg-gradient-to-r from-yellow-600 to-amber-600 hover:from-yellow-600/80 hover:to-amber-600/80 border-none"
            asChild
          >
            <a
              href={`https://discord.com/oauth2/authorize?client_id=769921109209907241&guild_id=${serverId}`}
              target="_blank"
              rel="noopener noreferrer"
            >
              Add Bot to Server
            </a>
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card className="border border-indigo-500/20 bg-indigo-950/10">
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-indigo-400" />
          Quick Connect
        </CardTitle>
        <CardDescription>Connect this server to a hub in seconds</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-sm text-gray-400 mb-4">
          Quickly connect this server to one of your hubs with just a few clicks.
        </p>
      </CardContent>
      <CardFooter>
        <Button className="cursor-pointer w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-600/80 hover:to-indigo-600/80 border-none">
          <Link href={`/dashboard/servers/${serverId}/connect`} passHref>
            Connect to Hub
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
