"use client";
import React from "react";
import { motion } from "framer-motion";
import { 
  BarChart3, 
  MessageSquare, 
  Server, 
  Users, 
  Activity, 
  Zap, 
  Shield, 
  Globe, 
  Bell 
} from "lucide-react";

// Map of icon names to components
const IconMap = {
  BarChart3,
  MessageSquare,
  Server,
  Users,
  Activity,
  Zap,
  Shield,
  Globe,
  Bell,
};

type IconName = keyof typeof IconMap;

interface StatCardProps {
  title: string;
  value: string;
  description: string;
  iconName: IconName;
  index: number;
  color?: "purple" | "blue" | "indigo" | "pink" | "emerald" | "orange";
}

export function StatCard({
  title,
  value,
  description,
  iconName,
  index,
  color = "purple",
}: StatCardProps) {
  const Icon = IconMap[iconName];

  const colorVariants = {
    purple: {
      gradient: "from-purple-500/20 via-violet-500/10 to-purple-600/20",
      border: "border-purple-400/30",
      iconBg: "bg-gradient-to-br from-purple-500 to-violet-600",
      iconGlow: "shadow-purple-500/50",
      textAccent: "text-purple-400",
      valueGradient: "bg-gradient-to-r from-purple-400 to-violet-400 bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-purple-500/25"
    },
    blue: {
      gradient: "from-blue-500/20 via-cyan-500/10 to-blue-600/20",
      border: "border-blue-400/30",
      iconBg: "bg-gradient-to-br from-blue-500 to-cyan-600",
      iconGlow: "shadow-blue-500/50",
      textAccent: "text-blue-400",
      valueGradient: "bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-blue-500/25"
    },
    indigo: {
      gradient: "from-indigo-500/20 via-blue-500/10 to-indigo-600/20",
      border: "border-indigo-400/30",
      iconBg: "bg-gradient-to-br from-indigo-500 to-blue-600",
      iconGlow: "shadow-indigo-500/50",
      textAccent: "text-indigo-400",
      valueGradient: "bg-gradient-to-r from-indigo-400 to-blue-400 bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-indigo-500/25"
    },
    pink: {
      gradient: "from-pink-500/20 via-rose-500/10 to-pink-600/20",
      border: "border-pink-400/30",
      iconBg: "bg-gradient-to-br from-pink-500 to-rose-600",
      iconGlow: "shadow-pink-500/50",
      textAccent: "text-pink-400",
      valueGradient: "bg-gradient-to-r from-pink-400 to-rose-400 bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-pink-500/25"
    },
    emerald: {
      gradient: "from-emerald-500/20 via-teal-500/10 to-emerald-600/20",
      border: "border-emerald-400/30",
      iconBg: "bg-gradient-to-br from-emerald-500 to-teal-600",
      iconGlow: "shadow-emerald-500/50",
      textAccent: "text-emerald-400",
      valueGradient: "bg-gradient-to-r from-emerald-400 to-teal-400 bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-emerald-500/25"
    },
    orange: {
      gradient: "from-orange-500/20 via-amber-500/10 to-orange-600/20",
      border: "border-orange-400/30",
      iconBg: "bg-gradient-to-br from-orange-500 to-amber-600",
      iconGlow: "shadow-orange-500/50",
      textAccent: "text-orange-400",
      valueGradient: "bg-gradient-to-r from-orange-400 to-amber-400 bg-clip-text text-transparent",
      hoverGlow: "hover:shadow-orange-500/25"
    },
  };

  const variant = colorVariants[color];

  return (
    <motion.div
      initial={{ opacity: 0, y: 30, scale: 0.9 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      whileHover={{ 
        y: -8, 
        scale: 1.03,
        transition: { duration: 0.2, type: "spring", stiffness: 300 }
      }}
      className="h-full group"
    >
      <div
        className={`
          relative h-full p-6 rounded-2xl border backdrop-blur-xl
          bg-gradient-to-br ${variant.gradient}
          ${variant.border} ${variant.hoverGlow}
          transition-all duration-300 ease-out
          hover:shadow-2xl hover:border-opacity-50
          before:absolute before:inset-0 before:rounded-2xl 
          before:bg-gradient-to-br before:from-white/5 before:to-transparent 
          before:pointer-events-none
          overflow-hidden
        `}
        style={{
          background: `
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
            linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.9) 100%)
          `
        }}
      >
        {/* Background decoration */}
        <div 
          className="absolute -top-4 -right-4 w-24 h-24 rounded-full opacity-10 blur-xl"
          style={{ background: `linear-gradient(135deg, ${variant.gradient.split(' ')[1]}, ${variant.gradient.split(' ')[3]})` }}
        />
        
        {/* Header */}
        <div className="flex items-start justify-between mb-6">
          <div className="space-y-2 flex-1">
            <h3 className="text-sm font-bold text-gray-300 uppercase tracking-widest">
              {title}
            </h3>
            <p className="text-xs text-gray-400 leading-relaxed max-w-[200px]">
              {description}
            </p>
          </div>
          
          {/* Icon with glow effect */}
          <motion.div
            className={`
              relative p-3 rounded-xl ${variant.iconBg} ${variant.iconGlow}
              shadow-lg group-hover:shadow-xl transition-all duration-300
            `}
            whileHover={{ rotate: 5, scale: 1.1 }}
            transition={{ type: "spring", stiffness: 300 }}
          >
            <Icon className="h-6 w-6 text-white" />
            <div className="absolute inset-0 rounded-xl bg-white/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </motion.div>
        </div>

        {/* Value */}
        <motion.div
          className="space-y-1"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
        >
          <div className={`text-5xl font-black tracking-tight ${variant.valueGradient}`}>
            {value}
          </div>
          
          {/* Subtle progress bar */}
          <div className="w-full h-1 bg-gray-800 rounded-full overflow-hidden">
            <motion.div
              className={`h-full bg-gradient-to-r ${variant.valueGradient.replace('bg-gradient-to-r', '').replace('bg-clip-text text-transparent', '')}`}
              initial={{ width: 0 }}
              animate={{ width: "75%" }}
              transition={{ duration: 1, delay: index * 0.1 + 0.5 }}
              style={{ background: `linear-gradient(to right, ${variant.gradient.split(' ')[1]}, ${variant.gradient.split(' ')[3]})` }}
            />
          </div>
        </motion.div>

        {/* Animated particles */}
        <div className="absolute inset-0 pointer-events-none overflow-hidden rounded-2xl">
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-1 h-1 bg-white/30 rounded-full"
              animate={{
                x: [0, 100, 0],
                y: [0, -50, 0],
                opacity: [0, 1, 0],
              }}
              transition={{
                duration: 3,
                delay: i * 0.5,
                repeat: Infinity,
                ease: "easeInOut",
              }}
              style={{
                left: `${20 + i * 30}%`,
                top: `${60 + i * 10}%`,
              }}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
}
