'use client';

import { motion } from 'motion/react';
import { useSearchParams } from 'next/navigation';
import { SearchBar } from './SearchBar';

/**
 * 1️⃣ Hero Section (Top) - Redesigned
 * Features headline, subtext, and large centered search bar
 */
export function HeroSection() {
  const searchParams = useSearchParams();

  // Get initial search term from URL params
  const initialSearchTerm = searchParams.get('search') || '';

  return (
    <div className="relative bg-gradient-to-br from-gray-950 via-gray-900 to-gray-950 overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute top-20 left-10 w-72 h-72 bg-blue-500/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-40 right-20 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute bottom-20 left-1/3 w-80 h-80 bg-indigo-500/10 rounded-full blur-3xl animate-pulse delay-2000" />
      </div>

      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          {/* Headline */}
          <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white via-blue-200 to-purple-200 drop-shadow-2xl">
            Discover Active Cross-Server Communities
          </h1>

          {/* Subtext */}
          <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed mb-12">
            Find, join, and grow your favorite hubs across Discord servers.
          </p>

          {/* 🔎 Search Bar (centered, large) */}
          <motion.div
            className="max-w-3xl mx-auto mb-8"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <div className="relative">
              <SearchBar
                initialValue={initialSearchTerm}
                placeholder="Search by hub name, topic, or tag..."
                size="large"
                variant="hero"
                showButton={true}
                autoFocus={false}
              />
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
