"use client";

import { useMutation, useQueryClient } from "@/lib/tanstack-query";
import { useToast } from "@/components/ui/use-toast";
import { fetchApi } from "@/lib/api";
import { hubKeys } from "./use-hub";
import { useRouter } from "next/navigation";

// Interface for hub update data
export interface HubUpdateData {
  name?: string;
  description?: string;
  private?: boolean;
  welcomeMessage?: string;
  rules?: string[];
  settings?: number;
  iconUrl?: string;
  bannerUrl?: string;
  language?: string;
  nsfw?: boolean;
}

// Update hub settings
export async function updateHub(hubId: string, data: HubUpdateData) {
  return fetchApi(`/api/hubs/${hubId}`, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
}

// Hook for updating hub settings
export function useUpdateHub(hubId: string) {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const router = useRouter();

  return useMutation({
    mutationFn: (data: HubUpdateData) => updateHub(hubId, data),
    onSuccess: () => {
      toast({
        title: "Hub updated",
        description: "Your changes have been saved successfully.",
        duration: 2000,
      });

      // Invalidate the hub query to refresh the data
      queryClient.invalidateQueries({ queryKey: hubKeys.detail(hubId) });

      // Refresh the page to show the updated data
      router.refresh();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update hub: ${error.message}`,
        variant: "destructive",
      });
    },
  });
}

// Delete a hub
export async function deleteHub(hubId: string) {
  return fetchApi(`/api/hubs/${hubId}`, {
    method: "DELETE",
  });
}

// Hook for deleting a hub
export function useDeleteHub() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const router = useRouter();

  return useMutation({
    mutationFn: deleteHub,
    onSuccess: () => {
      toast({
        title: "Hub deleted",
        description: "The hub has been deleted successfully.",
        duration: 2000,
      });

      // Invalidate all hub queries
      queryClient.invalidateQueries({ queryKey: hubKeys.all });

      // Redirect to the hubs page
      router.push("/dashboard");
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to delete hub: ${error.message}`,
        variant: "destructive",
      });
    },
  });
}
