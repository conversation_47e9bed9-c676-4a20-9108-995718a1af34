/**
 * Server router for tRPC
 */
import { db } from "@/lib/prisma";
import { REST } from "@discordjs/rest";
import { TRPCError } from "@trpc/server";
import { type APIChannel, type APIGuild, Routes } from "discord-api-types/v10";
import { z } from "zod/v4";
import { protectedProcedure, router } from "../trpc";

export const serverRouter = router({
	// Get a server by ID
	getServer: protectedProcedure
		.input(z.object({ serverId: z.string() }))
		.query(async ({ input }) => {
			const { serverId } = input;

			// Get bot token from environment variable
			const botToken = process.env.DISCORD_BOT_TOKEN;
			if (!botToken) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
                    message: "Bot token not configured"
                });
			}

			try {
				// Create REST instance
				const rest = new REST({ version: "10" }).setToken(botToken);

				// Fetch server from Discord API
				const discordServer = (await rest.get(
					Routes.guild(serverId),
				)) as APIGuild;

				// Get server from database
				const dbServer = await db.serverData.findUnique({
					where: { id: serverId },
				});

				// Format the response
				const server = {
					id: discordServer.id,
					name: discordServer.name,
					icon: discordServer.icon
						? `https://cdn.discordapp.com/icons/${discordServer.id}/${discordServer.icon}.png?size=128`
						: null,
					botAdded: !!dbServer,
				};

				return { server };
			} catch (error) {
				console.error("Error fetching server:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
                    error: "Failed to fetch server"
                });
			}
		}),

	// Get server channels
	getServerChannels: protectedProcedure
		.input(z.object({ serverId: z.string() }))
		.query(async ({ input }) => {
			const { serverId } = input;

			// Get bot token from environment variable
			const botToken = process.env.DISCORD_BOT_TOKEN;
			if (!botToken) {
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
                    error: "Bot token not configured"
                });
			}

			try {
				// Create REST instance
				const rest = new REST({ version: "10" }).setToken(botToken);

				// Fetch channels from Discord API
				const discordChannels = (await rest.get(
					Routes.guildChannels(serverId),
				)) as APIChannel[];

				// Filter and format channels
				const channels = discordChannels
					.filter((channel) => channel.type === 0) // Only text channels
					.map((channel) => ({
						id: channel.id,
						name: channel.name,
						type: channel.type,
					}));

				return { channels };
			} catch (error) {
				console.error("Error fetching channels:", error);
				throw new TRPCError({
					code: "INTERNAL_SERVER_ERROR",
                    error: "Failed to fetch channels"
                });
			}
		}),

	// Connect server to hub
	connectServerToHub: protectedProcedure
		.input(
			z.object({
				serverId: z.string(),
				hubId: z.string(),
				channelId: z.string(),
			}),
		)
		.mutation(async ({ input, ctx }) => {
			const { serverId, hubId, channelId } = input;
			const userId = ctx.session.user.id;

			// Check if the hub exists
			const hub = await db.hub.findUnique({
				where: { id: hubId },
				select: {
					id: true,
					ownerId: true,
					moderators: {
						where: { userId },
						select: { role: true },
					},
				},
			});

			if (!hub) {
				throw new TRPCError({
					code: "NOT_FOUND",
                    error: "Hub not found"
                });
			}

			// Check if the user has permission to connect servers to this hub
			const isOwner = hub.ownerId === userId;
			const isModerator = hub.moderators.length > 0;

			if (!isOwner && !isModerator) {
				throw new TRPCError({
					code: "FORBIDDEN",
                    error: "You do not have permission to connect servers to this hub"
                });
			}

			// Check if the server is already connected to a hub
			const existingConnection = await db.connection.findFirst({
				where: {
					serverId,
					connected: true,
				},
			});

			if (existingConnection) {
				throw new TRPCError({
					code: "BAD_REQUEST",
                    error: "Server is already connected to a hub"
                });
			}

			// Create the connection
			const connection = await db.connection.create({
				data: {
					serverId,
					hubId,
					channelId,
					webhookURL: "", // FIXME: This would be set by the bot
					connected: true,
					lastActive: new Date(),
				},
			});

			return { connection };
		}),

	// Disconnect server from hub
	disconnectServerFromHub: protectedProcedure
		.input(
			z.object({
				connectionId: z.string(),
			}),
		)
		.mutation(async ({ input, ctx }) => {
			const { connectionId } = input;
			const userId = ctx.session.user.id;

			// Get the connection with hub info
			const connection = await db.connection.findUnique({
				where: { id: connectionId },
				include: {
					hub: {
						select: {
							ownerId: true,
							moderators: {
								where: { userId },
								select: { role: true },
							},
						},
					},
				},
			});

			if (!connection) {
				throw new TRPCError({
					code: "NOT_FOUND",
                    error: "Connection not found"
                });
			}

			// Check if the user has permission to disconnect servers from this hub
			const isOwner = connection.hub.ownerId === userId;
			const isModerator = connection.hub.moderators.length > 0;

			if (!isOwner && !isModerator) {
				throw new TRPCError({
					code: "FORBIDDEN",
                    error: "You do not have permission to disconnect servers from this hub"
                });
			}

			// Update the connection
			const updatedConnection = await db.connection.update({
				where: { id: connectionId },
				data: {
					connected: false,
				},
			});

			return { connection: updatedConnection };
		}),
});
